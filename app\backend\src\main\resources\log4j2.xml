<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="info">
    <Appenders>
        <Console name="console" target="SYSTEM_OUT" follow="true">
            <JsonLayout compact="true" eventEol="true">
                <KeyValuePair key="timestamp" value="$${date:yyyy-MM-dd'T'HH:mm:ss.SSSZ}" />
                <KeyValuePair key="profile" value="${env:SPRING_PROFILES_ACTIVE}"/>
            </JsonLayout>
        </Console>
    </Appenders>
    <Loggers>
        <Logger level="debug" name="org.hibernate.SQL">
            <AppenderRef ref="console" />
        </Logger>
        <Root level="info">
            <AppenderRef ref="console" />
        </Root>
    </Loggers>
</Configuration>
