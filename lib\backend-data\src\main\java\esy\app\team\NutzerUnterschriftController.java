package esy.app.team;

import esy.EsyBackendAware;
import esy.api.team.NutzerUnterschrift;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.transaction.Transactional;
import java.util.UUID;

@RestController
@RequestMapping(EsyBackendAware.API_PATH)
@RequiredArgsConstructor
public class NutzerUnterschriftController {

    private final NutzerRepository nutzerURepository;

    private final NutzerUnterschriftRepository nutzerUnterschriftRepository;

    static final String SVG_XML_VALUE = "image/svg+xml";

    @GetMapping(
            path = "/nutzer/{id}/unterschrift.svg",
            produces = SVG_XML_VALUE)
    public ResponseEntity<String> downloadUnterschrift(@PathVariable("id") final UUID nutzerId) {
        return nutzerUnterschriftRepository.findById(nutzerId)
                .map(value -> ResponseEntity.ok().body(value.getSvg()))
                .orElseGet(() -> ResponseEntity.ok().body(NutzerUnterschrift.EMPTY_SVG));
    }

    @Transactional
    @PutMapping(
            path = "/nutzer/{id}/unterschrift.svg",
            consumes = SVG_XML_VALUE)
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void uploadUnterschrift(@PathVariable("id") final UUID nutzerId, @RequestBody final String svg) {
        nutzerUnterschriftRepository.save(
                nutzerUnterschriftRepository.findById(nutzerId)
                        .orElseGet(() -> NutzerUnterschrift.of(nutzerURepository.getReferenceById(nutzerId)))
                        .setSvg(svg));
    }

    @Transactional
    @DeleteMapping("/nutzer/{id}/unterschrift.svg")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void deleteUnterschrift(@PathVariable("id") final UUID nutzerId) {
        nutzerUnterschriftRepository.deleteById(nutzerId);
    }
}
