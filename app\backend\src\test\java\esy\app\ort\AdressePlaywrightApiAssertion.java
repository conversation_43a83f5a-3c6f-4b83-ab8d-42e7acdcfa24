package esy.app.ort;

import com.microsoft.playwright.Playwright;
import com.microsoft.playwright.options.RequestOptions;
import esy.api.ort.Adresse;
import esy.api.team.Gruppe;
import esy.app.PlaywrightApiAssertionBase;
import esy.app.PlaywrightApiTestEnv;
import esy.json.JsonMapper;
import lombok.NonNull;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;

import java.util.UUID;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.jupiter.api.Assertions.*;

@SuppressWarnings("java:S1192") // duplicating literals is ok
public class AdressePlaywrightApiAssertion extends PlaywrightApiAssertionBase {

    public AdressePlaywrightApiAssertion(@NonNull final Playwright playwright, @NonNull final PlaywrightApiTestEnv env) {
        super(playwright, env);
        login();
    }

    public void assertAdresse() {
        final var anschrift1 = "{\"strasse\":\"Almweg 1\",\"plz\":\"5020\",\"ort\":\"Salzburg\",\"land\":\"AT\"}";
        final var adresseId1 = doWithApi(
                (api) -> api.post("/api/adresse", RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"anschrift\":%s,\"aktiv\":\"false\"}", anschrift1))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.CREATED.value()));
                    final var json = Adresse.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals("Almweg 1", json.getStrasse());
                    assertEquals("5020", json.getPlz());
                    assertEquals("Salzburg", json.getOrt());
                    assertEquals("AT", json.getLand());
                    assertFalse(json.isAktiv());
                    return json.getId();
                });

        final var anschrift2 = "{\"strasse\":\"Bergstrasse 2\",\"plz\":\"5020\",\"ort\":\"Salzburg\",\"land\":\"AT\"}";
        final var adresseId2 = doWithApi(
                (api) -> api.put("/api/adresse/" + UUID.randomUUID(), RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"anschrift\":%s,\"aktiv\":\"false\"}", anschrift2))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.CREATED.value()));
                    final var json = Adresse.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals("Bergstrasse 2", json.getStrasse());
                    assertEquals("5020", json.getPlz());
                    assertEquals("Salzburg", json.getOrt());
                    assertEquals("AT", json.getLand());
                    assertFalse(json.isAktiv());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.put("/api/adresse/" + adresseId2, RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"anschrift\":%s,\"aktiv\":\"true\"}", anschrift2))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Adresse.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals("Bergstrasse 2", json.getStrasse());
                    assertEquals("5020", json.getPlz());
                    assertEquals("Salzburg", json.getOrt());
                    assertEquals("AT", json.getLand());
                    assertTrue(json.isAktiv());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.patch("/api/adresse/" + adresseId2, RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, "application/merge-patch+json")
                        .setData("{\"anschrift\":{\"plz\":\"5400\",\"ort\":\"Hallein\"}}")),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Adresse.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals("5400", json.getPlz());
                    assertEquals("Hallein", json.getOrt());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.get("/api/adresse"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var jsonReader = new JsonMapper().parseJsonPath(res.text());
                    assertNotNull(jsonReader.read("$.page"));
                    final var allAdresseId = jsonReader.readContentId();
                    assertTrue(allAdresseId.contains(adresseId1.toString()));
                    assertTrue(allAdresseId.contains(adresseId2.toString()));
                    return allAdresseId;
                });

        doWithApi(
                (api) -> api.get("/api/adresse", RequestOptions.create()
                        .setQueryParam("anschrift.ort", "salzburg")),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var jsonReader = new JsonMapper().parseJsonPath(res.text());
                    final var allAdresseId = jsonReader.readContentId();
                    assertTrue(allAdresseId.contains(adresseId1.toString()));
                    assertFalse(allAdresseId.contains(adresseId2.toString()));
                    return allAdresseId;
                });

        doWithApi(
                (api) -> api.get("/api/adresse/" + adresseId1),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Gruppe.parseJson(res.text());
                    assertEquals(adresseId1, json.getId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.delete("/api/adresse/" + adresseId1),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Gruppe.parseJson(res.text());
                    assertEquals(adresseId1, json.getId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.delete("/api/adresse/" + adresseId2),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Gruppe.parseJson(res.text());
                    assertEquals(adresseId2, json.getId());
                    return json.getId();
                });
    }
}
