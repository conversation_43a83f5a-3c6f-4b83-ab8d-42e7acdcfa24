package esy.app.info;

import esy.api.info.Enum;
import esy.auth.JwtRole;
import lombok.RequiredArgsConstructor;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.DataRetrievalFailureException;
import org.springframework.data.history.Revision;
import org.springframework.data.rest.webmvc.BasePathAwareController;
import org.springframework.hateoas.CollectionModel;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@RestController
@BasePathAwareController
@RequiredArgsConstructor
public class EnumRestController {

    private final EnumRepository enumRepository;

    @GetMapping("/enum/{art}")
    public ResponseEntity<CollectionModel<Enum>> getAllEnum(@PathVariable("art") final String art) {
        final var allValue = enumRepository.findAll(art);
        return ResponseEntity.status(HttpStatus.OK).body(CollectionModel.of(allValue));
    }

    @GetMapping("/enum/{id}/history")
    public ResponseEntity<CollectionModel<Enum>> getAllEnumHistory(@PathVariable("id") final UUID id) {
        final var allValue = enumRepository.findRevisions(id)
                .stream()
                .map(Revision::getEntity)
                .toList();
        return ResponseEntity.status(HttpStatus.OK).body(CollectionModel.of(allValue));
    }

    @PostMapping("/enum/{art}")
    @PreAuthorize(JwtRole.VERWALTUNG_EL)
    public ResponseEntity<Enum> createEnum(@PathVariable("art") final String art, @RequestBody final Enum body) {
        final var value = enumRepository.save(
                Enum.parseJson("{}")
                        .setArt(art)
                        .setCode(body.getCode())
                        .setName(body.getName())
                        .setText(body.getText()));
        return ResponseEntity.status(HttpStatus.CREATED).body(value);
    }

    @PutMapping("/enum/{art}/{code}")
    @PreAuthorize(JwtRole.VERWALTUNG_EL)
    public ResponseEntity<Enum> updateEnum(@PathVariable("art") final String art, @PathVariable("code") final Long code, @RequestBody final Enum body) {
        final var value = enumRepository.save(
                enumRepository.findByCode(art, code)
                        .orElseThrow(() ->
                                new DataRetrievalFailureException("Enum(" + art + ", " + code + ") not found"))
                        .setName(body.getName())
                        .setText(body.getText()));
        return ResponseEntity.status(HttpStatus.OK).body(value);
    }

    @DeleteMapping("/enum/{art}/{code}")
    @PreAuthorize(JwtRole.VERWALTUNG_EL)
    public ResponseEntity<Enum> deleteEnum(@PathVariable("art") final String art, @PathVariable("code") final Long code) {
        final var value = enumRepository.findByCode(art, code)
                .orElseThrow(() ->
                        new DataRetrievalFailureException("Enum(" + art + ", " + code + ") not found"));
        if (value.isKonstant()) {
            throw new DataIntegrityViolationException("Enum(" + art + ", " + code + ") not mutable");
        }
        enumRepository.delete(value);
        return ResponseEntity.status(HttpStatus.OK).body(value);
    }
}
