:icons: font
:experimental: true
= HELPME

== PostgreSQL

https://www.postgresql.org

https://www.postgresql.org/docs/current/app-psql.html

https://www.postgresqltutorial.com/postgresql-administration/psql-commands/

.psql environment variables
****
PGHOST::
behaves the same as the 
https://www.postgresql.org/docs/current/libpq-connect.html#LIBPQ-CONNECT-HOST[host]
connection parameter.

PGHOSTADDR::
behaves the same as the
https://www.postgresql.org/docs/current/libpq-connect.html#LIBPQ-CONNECT-HOSTADDR[hostaddr]
connection parameter.
This can be set instead of or in addition to PGHOST to avoid DNS lookup overhead.

PGPORT::
behaves the same as the
https://www.postgresql.org/docs/current/libpq-connect.html#LIBPQ-CONNECT-PORT[port]
connection parameter.

PGDATABASE::
behaves the same as the
https://www.postgresql.org/docs/current/libpq-connect.html#LIBPQ-CONNECT-DBNAME[dbname]
connection parameter.

PGUSER::
behaves the same as the
https://www.postgresql.org/docs/current/libpq-connect.html#LIBPQ-CONNECT-USER[user]
connection parameter.

PGPASSWORD::
behaves the same as the
https://www.postgresql.org/docs/current/libpq-connect.html#LIBPQ-CONNECT-PASSWORD[password]
connection parameter.
Use of this environment variable is not recommended for security reasons, as some operating systems allow non-root users to see process environment variables via ps; instead consider using a password file.

PGPASSFILE::
behaves the same as the
https://www.postgresql.org/docs/current/libpq-connect.html#LIBPQ-CONNECT-PASSFILE[passfile]
connection parameter.

PGCHANNELBINDING::
behaves the same as the
https://www.postgresql.org/docs/current/libpq-connect.html#LIBPQ-CONNECT-CHANNEL-BINDING[channel_binding]
connection parameter.

PGSERVICE::
behaves the same as the
https://www.postgresql.org/docs/current/libpq-connect.html#LIBPQ-CONNECT-SERVICE[service]
connection parameter.
****

https://www.pgadmin.org

https://www.adminer.org/

https://cloud.google.com/sql/docs/postgres

https://aws.amazon.com/de/rds/postgresql

== Kubernetes

https://docs.docker.com/desktop/kubernetes/

https://kubernetes.io/docs

https://github.com/kubernetes/kubectl

https://github.com/kubernetes/ingress-nginx

https://github.com/kubernetes/dashboard

https://kubernetes.io/docs/tasks/tools/

https://kubernetes.io/docs/tasks/tools/#kubectl

https://kubernetes.io/docs/tasks/tools/#minikube

https://rancher.com/docs/k3s/latest/en/

https://docs.docker.com/desktop/kubernetes/

https://kubernetes.io/docs/tasks/access-application-cluster/connecting-frontend-backend/

https://wkrzywiec.medium.com/deployment-of-multiple-apps-on-kubernetes-cluster-walkthrough-e05d37ed63d1

https://medium.com/@wkrzywiec/how-to-deploy-application-on-kubernetes-with-helm-39f545ad33b8

https://medium.com/@wkrzywiec/how-to-declaratively-run-helm-charts-using-helmfile-ac78572e6088

https://www.mirantis.com/blog/multi-container-pods-and-container-communication-in-kubernetes/

https://www.infoq.com/articles/zero-trust-k8s/

== Helm

https://github.com/helm/helm

https://helm.sh/

https://helm.sh/docs/topics/charts_hooks/

== Google

https://console.cloud.google.com/

https://console.cloud.google.com/kubernetes

https://spring.io/projects/spring-cloud-gcp

https://www.baeldung.com/spring-security-5-oauth2-login

https://dev.to/rhanarion/build-a-docker-image-on-gitlab-ci-and-publish-it-to-google-container-registry-6h8

https://dev.to/rhanarion/deploy-to-google-kubernetes-engine-using-gitlab-ci-42gb

https://dev.to/madeindjs/deploy-a-fullstack-application-on-google-cloud-plateform-with-gitlab-ci-1n91
