package esy.app.zeit;

import com.microsoft.playwright.Playwright;
import com.microsoft.playwright.options.RequestOptions;
import esy.api.team.Nutzer;
import esy.api.zeit.Abwesenheit;
import esy.app.PlaywrightApiAssertionBase;
import esy.app.PlaywrightApiTestEnv;
import esy.json.JsonMapper;
import lombok.NonNull;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;

import java.time.LocalDate;
import java.util.UUID;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.jupiter.api.Assertions.*;

@SuppressWarnings("java:S1192") // duplicating literals is ok
public class AbwesenheitPlaywrightApiAssertion extends PlaywrightApiAssertionBase {

    public AbwesenheitPlaywrightApiAssertion(@NonNull final Playwright playwright, @NonNull final PlaywrightApiTestEnv env) {
        super(playwright, env);
        login();
    }

    public void assertAbwesenheit() {
        final var nutzer1 = createRandomNutzer();

        final var datum1 = LocalDate.of(2020, 4, 22);
        final var abwesenheitId1 = doWithApi(
                (api) -> api.post("/api/abwesenheit", RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"datum\":\"%s\",\"nutzerId\":\"%s\"}",
                                datum1, nutzer1.getId()))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.CREATED.value()));
                    final var json = Abwesenheit.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(datum1, json.getDatum());
                    assertEquals(nutzer1.getId(), json.getNutzerId());
                    return json.getId();
                });

        final var datum2 = LocalDate.of(2020, 4, 23);
        final var abwesenheitId2 = doWithApi(
                (api) -> api.put("/api/abwesenheit/" + UUID.randomUUID(), RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"datum\":\"%s\",\"nutzerId\":\"%s\"}",
                                datum2, nutzer1.getId()))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.CREATED.value()));
                    final var json = Abwesenheit.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(datum2, json.getDatum());
                    assertEquals(nutzer1.getId(), json.getNutzerId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.get("/api/abwesenheit"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var jsonReader = new JsonMapper().parseJsonPath(res.text());
                    assertNotNull(jsonReader.read("$.page"));
                    final var allAbwesenheitId = jsonReader.readContentId();
                    assertTrue(allAbwesenheitId.contains(abwesenheitId1.toString()));
                    assertTrue(allAbwesenheitId.contains(abwesenheitId2.toString()));
                    return allAbwesenheitId;
                });

        doWithApi(
                (api) -> api.get("/api/abwesenheit/search/findAllByNutzerAndJahrAndMonat", RequestOptions.create()
                        .setQueryParam("nutzerId", nutzer1.getId().toString())
                        .setQueryParam("jahr", datum1.getYear())
                        .setQueryParam("monat", datum1.getMonthValue())),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var jsonReader = new JsonMapper().parseJsonPath(res.text());
                    final var allAbwesenheitId = jsonReader.readContentId();
                    assertTrue(allAbwesenheitId.contains(abwesenheitId1.toString()));
                    assertTrue(allAbwesenheitId.contains(abwesenheitId2.toString()));
                    return allAbwesenheitId;
                });

        doWithApi(
                (api) -> api.get("/api/abwesenheit/search/findAllByNutzerAndDatumBetween", RequestOptions.create()
                        .setQueryParam("nutzerId", nutzer1.getId().toString())
                        .setQueryParam("von", datum1.getYear() + "-04-21")
                        .setQueryParam("bis", datum1.getYear() + "-04-22")),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var jsonReader = new JsonMapper().parseJsonPath(res.text());
                    final var allAbwesenheitId = jsonReader.readContentId();
                    assertTrue(allAbwesenheitId.contains(abwesenheitId1.toString()));
                    assertFalse(allAbwesenheitId.contains(abwesenheitId2.toString()));
                    return allAbwesenheitId;
                });

        doWithApi(
                (api) -> api.delete("/api/abwesenheit/" + abwesenheitId1),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Abwesenheit.parseJson(res.text());
                    assertEquals(abwesenheitId1, json.getId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.delete("/api/abwesenheit/" + abwesenheitId2),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Abwesenheit.parseJson(res.text());
                    assertEquals(abwesenheitId2, json.getId());
                    return json.getId();
                });
    }

    public void assertAbwesenheitBesitzer() {
        final var nutzer1 = createRandomNutzer();
        final var nutzer2 = createRandomNutzer();

        final var datum1 = LocalDate.of(2020, 4, 22);
        final var abwesenheitId1 = doWithApi(
                (api) -> api.post("/api/abwesenheit", RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"datum\":\"%s\",\"nutzerId\":\"%s\"}",
                                datum1, nutzer1.getId()))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.CREATED.value()));
                    final var json = Abwesenheit.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(datum1, json.getDatum());
                    assertEquals(nutzer1.getId(), json.getNutzerId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.get("/api/abwesenheit/" + abwesenheitId1 + "/nutzer"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Nutzer.parseJson(res.text());
                    assertEquals(nutzer1.getId(), json.getId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.patch("/api/abwesenheit/" + abwesenheitId1, RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, "application/merge-patch+json")
                        .setData(String.format("{\"nutzerId\":\"%s\"}",
                                nutzer2.getId()))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Abwesenheit.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(nutzer2.getId(), json.getNutzerId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.get("/api/abwesenheit/" + abwesenheitId1 + "/nutzer"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Nutzer.parseJson(res.text());
                    assertEquals(nutzer2.getId(), json.getId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.delete("/api/abwesenheit/" + abwesenheitId1),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Abwesenheit.parseJson(res.text());
                    assertEquals(abwesenheitId1, json.getId());
                    return json.getId();
                });
    }
}
