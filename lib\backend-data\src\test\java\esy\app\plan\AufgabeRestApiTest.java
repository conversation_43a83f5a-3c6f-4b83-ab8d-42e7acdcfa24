package esy.app.plan;

import esy.api.plan.Aufgabe;
import esy.api.plan.Terminserie;
import esy.api.plan.VorgangStatus;
import esy.app.EsyBackendConfiguration;
import esy.app.team.NutzerRepository;
import esy.auth.JwtRole;
import esy.auth.WithMockJwt;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.restdocs.RestDocumentationContextProvider;
import org.springframework.restdocs.RestDocumentationExtension;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.UUID;

import static esy.app.RestApiAssertions.assertRestApiSpecExists;
import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.restdocs.mockmvc.MockMvcRestDocumentation.document;
import static org.springframework.restdocs.mockmvc.MockMvcRestDocumentation.documentationConfiguration;
import static org.springframework.restdocs.operation.preprocess.Preprocessors.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@Tag("slow")
@SpringBootTest
@AutoConfigureMockMvc(addFilters = false)
@ContextConfiguration(classes = {EsyBackendConfiguration.class})
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@ExtendWith({MockitoExtension.class, RestDocumentationExtension.class})
class AufgabeRestApiTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private AufgabeRepository aufgabeRepository;

    @Autowired
    private NutzerRepository nutzerRepository;

    @Autowired
    private ProjektRepository projektRepository;

    @BeforeEach
    void setUp(final WebApplicationContext webApplicationContext,
               final RestDocumentationContextProvider restDocumentation) {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext)
                .apply(documentationConfiguration(restDocumentation))
                .alwaysDo(document("{method-name}",
                        preprocessRequest(prettyPrint()),
                        preprocessResponse(prettyPrint())))
                .build();
    }

    @Test
    @Order(1)
    void asciidoc() {
        assertRestApiSpecExists(Aufgabe.class);
    }

    @ParameterizedTest
    @ValueSource(strings = {"GET", "POST", "PUT", "PATCH", "DELETE"})
    @Order(1)
    void preflight(final String method) throws Exception {
        mockMvc.perform(options("/api/aufgabe")
                        .header("Access-Control-Request-Method", method)
                        .header("Access-Control-Request-Headers", "Content-Type")
                        .header("Origin", "http://localhost:5000")) // UI
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(header()
                        .exists("Access-Control-Allow-Origin"))
                .andExpect(header()
                        .exists("Access-Control-Allow-Methods"))
                .andExpect(header()
                        .exists("Access-Control-Allow-Headers"));
    }

    @Test
    @Order(10)
    @WithMockJwt
    void getApiAufgabeNoElement() throws Exception {
        mockMvc.perform(get("/api/aufgabe")
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(jsonPath("$.content")
                        .isArray())
                .andExpect(jsonPath("$.content[0]")
                        .doesNotExist());
    }

    @Sql("/sql/nutzer.sql")
    @Sql("/sql/projekt.sql")
    @Test
    @Order(20)
    @WithMockJwt
    void postApiAufgabe() throws Exception {
        final var titel = "Aufgabe A";
        assertEquals(0, aufgabeRepository.findAll().stream()
                .filter(e -> e.getTitel().equals(titel))
                .count());
        mockMvc.perform(post("/api/aufgabe")
                        .content("{" +
                                "\"projektId\":1," +
                                "\"titel\":\"" + titel + "\"," +
                                "\"termin\":\"2019-04-22\"" +
                                "}")
                        .contentType(MediaType.APPLICATION_JSON)
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isCreated())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .string("ETag", "\"0\""))
                .andExpect(jsonPath("$.id")
                        .isNotEmpty())
                .andExpect(jsonPath("$.projektId")
                        .exists())
                .andExpect(jsonPath("$.nutzerId")
                        .doesNotExist())
                .andExpect(jsonPath("$.titel")
                        .value(titel))
                .andExpect(jsonPath("$.text")
                        .isEmpty())
                .andExpect(jsonPath("$.termin")
                        .value("2019-04-22"))
                .andExpect(jsonPath("$.status")
                        .value(VorgangStatus.I.name()))
                .andExpect(jsonPath("$.allStichwort")
                        .isArray())
                .andExpect(jsonPath("$.allStichwort[0]")
                        .doesNotExist())
                .andExpect(jsonPath("$.quelle")
                        .doesNotExist());
    }

    @Test
    @Order(21)
    @WithMockJwt
    void postApiAufgabeAgain() throws Exception {
        final var titel = "Aufgabe A";
        assertEquals(1, aufgabeRepository.findAll().stream()
                .filter(e -> e.getTitel().equals(titel))
                .count());
        mockMvc.perform(post("/api/aufgabe")
                        .content("{" +
                                "\"projektId\":1," +
                                "\"titel\":\"" + titel + "\"" +
                                "}")
                        .contentType(MediaType.APPLICATION_JSON)
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isCreated())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .string("ETag", "\"0\""))
                .andExpect(jsonPath("$.id")
                        .isNotEmpty())
                .andExpect(jsonPath("$.projektId")
                        .exists())
                .andExpect(jsonPath("$.nutzerId")
                        .doesNotExist())
                .andExpect(jsonPath("$.titel")
                        .value(titel))
                .andExpect(jsonPath("$.text")
                        .isEmpty())
                .andExpect(jsonPath("$.termin")
                        .value("2000-01-01"))
                .andExpect(jsonPath("$.status")
                        .value(VorgangStatus.I.name()))
                .andExpect(jsonPath("$.allStichwort")
                        .isArray())
                .andExpect(jsonPath("$.allStichwort[0]")
                        .doesNotExist())
                .andExpect(jsonPath("$.quelle")
                        .doesNotExist());
    }

    @Test
    @Order(30)
    @WithMockJwt
    void putApiAufgabeCreate() throws Exception {
        final var uuid = "c3333333-3bb4-2113-a010-cd42452ab140";
        final var titel = "Aufgabe C";
        final var termin = "2019-04-22";
        assertFalse(aufgabeRepository.findById(UUID.fromString(uuid)).isPresent());
        mockMvc.perform(put("/api/aufgabe/" + uuid)
                        .content("{" +
                                "\"projektId\":1," +
                                "\"titel\":\"" + titel + "\"," +
                                "\"termin\":\"" + termin + "\"," +
                                "\"status\":\"" + VorgangStatus.I + "\"" +
                                "}")
                        .contentType(MediaType.APPLICATION_JSON)
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isCreated())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .string("ETag", "\"0\""))
                .andExpect(jsonPath("$.id")
                        .value(uuid))
                .andExpect(jsonPath("$.projektId")
                        .exists())
                .andExpect(jsonPath("$.nutzerId")
                        .doesNotExist())
                .andExpect(jsonPath("$.titel")
                        .value(titel))
                .andExpect(jsonPath("$.text")
                        .isEmpty())
                .andExpect(jsonPath("$.termin")
                        .value(termin))
                .andExpect(jsonPath("$.status")
                        .value(VorgangStatus.I.name()))
                .andExpect(jsonPath("$.allStichwort")
                        .isArray())
                .andExpect(jsonPath("$.allStichwort[0]")
                        .doesNotExist())
                .andExpect(jsonPath("$.quelle")
                        .doesNotExist());
    }

    @Test
    @Order(31)
    @WithMockJwt
    void putApiAufgabeUpdate() throws Exception {
        final var uuid = "c3333333-3bb4-2113-a010-cd42452ab140";
        final var aufgabe = aufgabeRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        mockMvc.perform(put("/api/aufgabe/" + aufgabe.getId())
                        .content("{" +
                                "\"projektId\":1," +
                                "\"titel\":\"" + aufgabe.getTitel() + "\"," +
                                "\"termin\":\"" + aufgabe.getTermin() + "\"," +
                                "\"status\":\"" + VorgangStatus.B + "\"" +
                                "}")
                        .contentType(MediaType.APPLICATION_JSON)
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + aufgabe.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .string("ETag", "\"1\""))
                .andExpect(jsonPath("$.id")
                        .value(aufgabe.getId().toString()))
                .andExpect(jsonPath("$.projektId")
                        .exists())
                .andExpect(jsonPath("$.nutzerId")
                        .doesNotExist())
                .andExpect(jsonPath("$.titel")
                        .value(aufgabe.getTitel()))
                .andExpect(jsonPath("$.text")
                        .value(aufgabe.getText()))
                .andExpect(jsonPath("$.termin")
                        .value(aufgabe.getTermin().toString()))
                .andExpect(jsonPath("$.status")
                        .value(VorgangStatus.B.name()))
                .andExpect(jsonPath("$.allStichwort")
                        .isArray())
                .andExpect(jsonPath("$.allStichwort[0]")
                        .doesNotExist())
                .andExpect(jsonPath("$.quelle")
                        .doesNotExist());
    }

    @Test
    @Order(32)
    @WithMockJwt
    void patchApiAufgabeProjekt() throws Exception {
        final var uuid = "c3333333-3bb4-2113-a010-cd42452ab140";
        final var aufgabe = aufgabeRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        mockMvc.perform(patch("/api/aufgabe/" + aufgabe.getId())
                        .content("{" +
                                "\"projektId\":2" +
                                "}")
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + aufgabe.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(aufgabe.getId().toString()))
                .andExpect(jsonPath("$.projektId")
                        .exists());
    }

    @Test
    @Order(33)
    @WithMockJwt(roles = {JwtRole.VERWALTUNG})
    void patchApiAufgabeProjektNull() throws Exception {
        final var uuid = "c3333333-3bb4-2113-a010-cd42452ab140";
        final var aufgabe = aufgabeRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        mockMvc.perform(patch("/api/aufgabe/" + aufgabe.getId())
                        .content("{" +
                                "\"projektId\":null" +
                                "}")
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + aufgabe.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isConflict());
    }

    @Test
    @Order(34)
    @WithMockJwt
    void patchApiAufgabeNutzer() throws Exception {
        final var uuid = "c3333333-3bb4-2113-a010-cd42452ab140";
        final var aufgabe = aufgabeRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        mockMvc.perform(patch("/api/aufgabe/" + aufgabe.getId())
                        .content("{" +
                                "\"nutzerId\":\"a2222222-6ee8-4335-b12a-ef84794bd27a\"" +
                                "}")
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + aufgabe.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(aufgabe.getId().toString()))
                .andExpect(jsonPath("$.nutzerId")
                        .exists());
    }

    @Test
    @Order(35)
    @WithMockJwt
    void patchApiAufgabeNutzerNull() throws Exception {
        final var uuid = "c3333333-3bb4-2113-a010-cd42452ab140";
        final var aufgabe = aufgabeRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        mockMvc.perform(patch("/api/aufgabe/" + aufgabe.getId())
                        .content("{" +
                                "\"nutzerId\":null" +
                                "}")
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + aufgabe.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(aufgabe.getId().toString()))
                .andExpect(jsonPath("$.nutzerId")
                        .doesNotExist());
    }

    @ParameterizedTest
    @ValueSource(strings = {
            "Vitamin C",
            "Aufgabe C"
    })
    @Order(40)
    @WithMockJwt
    void patchApiAufgabeTitel(final String titel) throws Exception {
        final var uuid = "c3333333-3bb4-2113-a010-cd42452ab140";
        final var aufgabe = aufgabeRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        mockMvc.perform(patch("/api/aufgabe/" + aufgabe.getId())
                        .content("{" +
                                "\"titel\":\"" + titel + "\"" +
                                "}")
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + aufgabe.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(aufgabe.getId().toString()))
                .andExpect(jsonPath("$.titel")
                        .value(titel));
    }

    @Test
    @Order(41)
    @WithMockJwt
    void patchApiAufgabeText() throws Exception {
        final var uuid = "c3333333-3bb4-2113-a010-cd42452ab140";
        final var aufgabe = aufgabeRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        mockMvc.perform(patch("/api/aufgabe/" + aufgabe.getId())
                        .content("{" +
                                "\"text\":\"Lorem ipsum dolor sit amet.\"" +
                                "}")
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + aufgabe.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(aufgabe.getId().toString()))
                .andExpect(jsonPath("$.text")
                        .isNotEmpty());
    }

    @ParameterizedTest
    @ValueSource(strings = {"X", "I", "A", "B"})
    @Order(42)
    @WithMockJwt
    void patchApiAufgabeStatus(final String status) throws Exception {
        final var uuid = "c3333333-3bb4-2113-a010-cd42452ab140";
        final var aufgabe = aufgabeRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        mockMvc.perform(patch("/api/aufgabe/" + aufgabe.getId())
                        .content("{" +
                                "\"status\":\"" + status + "\"" +
                                "}")
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + aufgabe.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(aufgabe.getId().toString()))
                .andExpect(jsonPath("$.status")
                        .value(status));
    }

    @Test
    @Order(42)
    @WithMockJwt
    void patchApiAufgabeStatusConflict() throws Exception {
        final var uuid = "c3333333-3bb4-2113-a010-cd42452ab140";
        final var aufgabe = aufgabeRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        mockMvc.perform(patch("/api/aufgabe/" + aufgabe.getId())
                        .content("{" +
                                "\"status\":\"I\"" +
                                "}")
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + aufgabe.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isConflict());
    }

    @ParameterizedTest
    @ValueSource(strings = {
            "2000-01-01",
            "2031-04-22"
    })
    @Order(43)
    @WithMockJwt
    void patchApiAufgabeTermin(final String datum) throws Exception {
        final var uuid = "c3333333-3bb4-2113-a010-cd42452ab140";
        final var aufgabe = aufgabeRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        mockMvc.perform(patch("/api/aufgabe/" + aufgabe.getId())
                        .content("{" +
                                "\"termin\":\"" + datum + "\"" +
                                "}")
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + aufgabe.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(aufgabe.getId().toString()))
                .andExpect(jsonPath("$.termin")
                        .value(datum));
    }

    @ParameterizedTest
    @ValueSource(strings = {"J", "M", "W", "T"})
    @Order(44)
    @WithMockJwt
    void patchApiAufgabeTerminserie(final String serie) throws Exception {
        final var uuid = "c3333333-3bb4-2113-a010-cd42452ab140";
        final var aufgabe = aufgabeRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        mockMvc.perform(patch("/api/aufgabe/" + aufgabe.getId())
                        .content("{" +
                                "\"text\":\"patchApiAufgabeTerminserie\"," +
                                "\"terminserie\":\"" + Terminserie.valueOf(serie) + "\"" +
                                "}")
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + aufgabe.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(aufgabe.getId().toString()))
                .andExpect(jsonPath("$.terminserie")
                        .value(serie));
    }

    @Test
    @Order(45)
    @WithMockJwt
    void patchApiAufgabeStichwortA() throws Exception {
        final var uuid = "c3333333-3bb4-2113-a010-cd42452ab140";
        final var aufgabe = aufgabeRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        mockMvc.perform(patch("/api/aufgabe/" + aufgabe.getId())
                        .content("{" +
                                "\"allStichwort\":[\"A\"]" +
                                "}")
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + aufgabe.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(aufgabe.getId().toString()))
                .andExpect(jsonPath("$.allStichwort")
                        .isArray())
                .andExpect(jsonPath("$.allStichwort[0]")
                        .value("A"))
                .andExpect(jsonPath("$.allStichwort[1]")
                        .doesNotExist());
    }

    @Test
    @Order(46)
    @WithMockJwt
    void patchApiAufgabeStichwortB() throws Exception {
        final var uuid = "c3333333-3bb4-2113-a010-cd42452ab140";
        final var aufgabe = aufgabeRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        mockMvc.perform(patch("/api/aufgabe/" + aufgabe.getId())
                        .content("{" +
                                "\"allStichwort\":[\"B\"]" +
                                "}")
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + aufgabe.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(aufgabe.getId().toString()))
                .andExpect(jsonPath("$.allStichwort")
                        .isArray())
                .andExpect(jsonPath("$.allStichwort[0]")
                        .value("B"))
                .andExpect(jsonPath("$.allStichwort[1]")
                        .doesNotExist());
    }

    @Test
    @Order(47)
    @WithMockJwt
    void patchApiAufgabeQuelle() throws Exception {
        final var uuid = "c3333333-3bb4-2113-a010-cd42452ab140";
        final var aufgabe = aufgabeRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        mockMvc.perform(patch("/api/aufgabe/" + aufgabe.getId())
                        .content("{" +
                                "\"text\":\"patchApiAufgabeQuelle\"," +
                                "\"quelle\": {" +
                                "  \"typ\":\"JIRA\"," +
                                "  \"uri\":\"https://cardsplus.atlassian.net/browse/DEMOSCS-1\"," +
                                "  \"text\":\"JIRA-Issue DEMOSCS-1\"" +
                                "}" +
                                "}")
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + aufgabe.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(aufgabe.getId().toString()))
                .andExpect(jsonPath("$.quelle.typ")
                        .value("JIRA"))
                .andExpect(jsonPath("$.quelle.uri")
                        .value("https://cardsplus.atlassian.net/browse/DEMOSCS-1"))
                .andExpect(jsonPath("$.quelle.text")
                        .value("JIRA-Issue DEMOSCS-1"));
    }

    @Test
    @Order(48)
    @WithMockJwt
    void patchApiAufgabeQuelleNull() throws Exception {
        final var uuid = "c3333333-3bb4-2113-a010-cd42452ab140";
        final var aufgabe = aufgabeRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        mockMvc.perform(patch("/api/aufgabe/" + aufgabe.getId())
                        .content("{" +
                                "\"text\":\"patchApiAufgabeQuelle\"," +
                                "\"quelle\":null" +
                                "}")
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + aufgabe.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(aufgabe.getId().toString()))
                .andExpect(jsonPath("$.quelle")
                        .doesNotExist());
    }

    @Test
    @Order(49)
    @WithMockJwt
    void patchApiAufgabeStatusTransition() throws Exception {
        final var uuid = "c3333333-3bb4-2113-a010-cd42452ab140";
        final var aufgabe = aufgabeRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        mockMvc.perform(patch("/api/aufgabe/" + aufgabe.getId() + "/" + VorgangStatus.A)
                        .contentType(MediaType.parseMediaType("plain/text"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + aufgabe.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(aufgabe.getId().toString()))
                .andExpect(jsonPath("$.status")
                        .value(VorgangStatus.A.name()))
                .andExpect(jsonPath("$.terminserie")
                        .value(Terminserie.T.name()))
                .andExpect(jsonPath("$.termin")
                        .value(aufgabe.getTermin().plusDays(1L).toString()));
    }

    @Test
    @Order(50)
    @WithMockJwt
    void getApiAufgabeById() throws Exception {
        final var uuid = "c3333333-3bb4-2113-a010-cd42452ab140";
        final var aufgabe = aufgabeRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        assertNotNull(aufgabe.getProjekt());
        mockMvc.perform(get("/api/aufgabe/" + aufgabe.getId())
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .string("ETag", "\"22\""))
                .andExpect(jsonPath("$.id")
                        .value(aufgabe.getId().toString()))
                .andExpect(jsonPath("$.projektId")
                        .exists())
                .andExpect(jsonPath("$.nutzerId")
                        .doesNotExist())
                .andExpect(jsonPath("$.titel")
                        .value(aufgabe.getTitel()))
                .andExpect(jsonPath("$.text")
                        .value(aufgabe.getText()))
                .andExpect(jsonPath("$.termin")
                        .value(aufgabe.getTermin().toString()))
                .andExpect(jsonPath("$.status")
                        .value(aufgabe.getStatus().name()))
                .andExpect(jsonPath("$.allStichwort")
                        .isArray())
                .andExpect(jsonPath("$.quelle")
                        .doesNotExist());
    }

    @Test
    @Order(51)
    @WithMockJwt
    void getApiAufgabeByIdNotFound() throws Exception {
        final var uuid = "00000000-6ee8-4335-b12a-ef84794bd27a";
        assertFalse(aufgabeRepository.findById(UUID.fromString(uuid)).isPresent());
        mockMvc.perform(get("/api/aufgabe/" + uuid)
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isNotFound());
    }

    @Test
    @Order(52)
    @WithMockJwt
    void getApiAufgabeByProjektAndJahrAndMonat() throws Exception {
        mockMvc.perform(get("/api/aufgabe/search/findAllByProjektAndJahrAndMonat")
                        .param("projektId", "1")
                        .param("jahr", "2019")
                        .param("monat", "4")
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(jsonPath("$.content")
                        .isArray())
                .andExpect(jsonPath("$.content[0]")
                        .exists())
                .andExpect(jsonPath("$.content[1]")
                        .doesNotExist());
    }

    @Test
    @Order(53)
    @WithMockJwt
    void getApiAufgabeProjektTerminDsl() throws Exception {
        final var datum = LocalDate.of(2019, 4, 22);
        mockMvc.perform(get("/api/aufgabe")
                        .param("projekt.id", "1")
                        .param("termin", datum.format(Aufgabe.DATE_FORMATTER))
                        .param("termin", datum.format(Aufgabe.DATE_FORMATTER))
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(jsonPath("$.content")
                        .isArray())
                .andExpect(jsonPath("$.content[0]")
                        .exists())
                .andExpect(jsonPath("$.content[1]")
                        .doesNotExist());
    }

    @Test
    @Order(54)
    @WithMockJwt
    void getApiAufgabeProjektIdDsl() throws Exception {
        mockMvc.perform(get("/api/aufgabe?projekt.id=1")
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(jsonPath("$.content")
                        .isArray())
                .andExpect(jsonPath("$.content[0]")
                        .exists())
                .andExpect(jsonPath("$.content[1]")
                        .exists())
                .andExpect(jsonPath("$.content[2]")
                        .doesNotExist());
    }

    @ParameterizedTest
    @ValueSource(strings = {
            "?termin=2019-04-22",
            "?termin=2019-04-22&termin=2019-04-22",
            "?termin=2019-04-01&termin=2019-04-30"
    })
    @Order(55)
    @WithMockJwt
    void getApiAufgabeTerminDsl(final String query) throws Exception {
        mockMvc.perform(get("/api/aufgabe" + query)
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(jsonPath("$.content")
                        .isArray())
                .andExpect(jsonPath("$.content[0]")
                        .exists())
                .andExpect(jsonPath("$.content[1]")
                        .doesNotExist());
    }

    @ParameterizedTest
    @ValueSource(strings = {
            "?termin=2020-04-22",
            "?termin=2020-04-22&termin=2020-04-22",
            "?termin=2020-04-01&termin=2020-04-30"
    })
    @Order(56)
    @WithMockJwt
    void getApiAufgabeTerminDslNotFound(final String query) throws Exception {
        mockMvc.perform(get("/api/aufgabe" + query)
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(jsonPath("$.content")
                        .isArray())
                .andExpect(jsonPath("$.content[0]")
                        .doesNotExist());
    }

    @ParameterizedTest
    @ValueSource(strings = {
            "Aufgabe",
            "aufgabe%"
    })
    @Order(57)
    @WithMockJwt
    void getApiAufgabeTitelDsl(final String value) throws Exception {
        mockMvc.perform(get("/api/aufgabe")
                        .param("titel", value)
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(jsonPath("$.content")
                        .isArray())
                .andExpect(jsonPath("$.content[0]")
                        .exists())
                .andExpect(jsonPath("$.content[1]")
                        .exists())
                .andExpect(jsonPath("$.content[2]")
                        .exists())
                .andExpect(jsonPath("$.content[3]")
                        .doesNotExist());
    }

    @Test
    @Order(58)
    @WithMockJwt
    void getApiAufgabeTextDslNotFound() throws Exception {
        mockMvc.perform(get("/api/aufgabe")
                        .param("text", "task")
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(jsonPath("$.content")
                        .isArray())
                .andExpect(jsonPath("$.content[0]")
                        .doesNotExist());
    }

    @Test
    @Order(80)
    @WithMockJwt(email = "<EMAIL>")
    void deleteApiAufgabeForbidden() throws Exception {
        final var nutzer = nutzerRepository.findById(UUID.fromString("a2222222-6ee8-4335-b12a-ef84794bd27a"))
                .orElseThrow();
        final var uuid = "c3333333-3bb4-2113-a010-cd42452ab140";
        final var aufgabe = aufgabeRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        aufgabeRepository.save(aufgabe.setNutzer(nutzer));
        mockMvc.perform(delete("/api/aufgabe/" + uuid)
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isForbidden());
    }

    @Test
    @Order(81)
    @WithMockJwt(email = "<EMAIL>")
    void deleteApiAufgabe() throws Exception {
        final var nutzer = nutzerRepository.findById(UUID.fromString("a2222222-6ee8-4335-b12a-ef84794bd27a"))
                .orElseThrow();
        final var uuid = "c3333333-3bb4-2113-a010-cd42452ab140";
        final var aufgabe = aufgabeRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        aufgabeRepository.save(aufgabe.setNutzer(nutzer));
        mockMvc.perform(delete("/api/aufgabe/" + uuid)
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .doesNotExist("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(uuid));
    }

    @Test
    @Order(82)
    @WithMockJwt(roles = {JwtRole.VERWALTUNG})
    void deleteApiAufgabeNotFound() throws Exception {
        final var uuid = "c3333333-3bb4-2113-a010-cd42452ab140";
        assertFalse(aufgabeRepository.findById(UUID.fromString(uuid)).isPresent());
        mockMvc.perform(delete("/api/aufgabe/" + uuid)
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isNotFound());
    }

    @Test
    @Order(90)
    @WithMockJwt
    void getApiAufgabe() throws Exception {
        mockMvc.perform(get("/api/aufgabe")
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(jsonPath("$.content")
                        .isArray())
                .andExpect(jsonPath("$.content[0]")
                        .exists())
                .andExpect(jsonPath("$.content[1]")
                        .exists())
                .andExpect(jsonPath("$.content[2]")
                        .doesNotExist());
    }

    @Test
    @Order(91)
    void getApiAufgabeHistory() throws Exception {
        final var uuid = "c3333333-3bb4-2113-a010-cd42452ab140";
        mockMvc.perform(get(String.format("/api/aufgabe/%s/history", uuid))
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(jsonPath("$.content")
                        .isArray())
                // putApiAufgabeCreate
                .andExpect(jsonPath("$.content[0].id")
                        .value(uuid))
                .andExpect(jsonPath("$.content[0].projektId")
                        .value(1L))
                .andExpect(jsonPath("$.content[0].nutzerId")
                        .doesNotExist())
                .andExpect(jsonPath("$.content[0].titel")
                        .value("Aufgabe C"))
                .andExpect(jsonPath("$.content[0].text")
                        .isEmpty())
                .andExpect(jsonPath("$.content[0].termin")
                        .value("2019-04-22"))
                .andExpect(jsonPath("$.content[0].terminserie")
                        .value(Terminserie.X.name()))
                .andExpect(jsonPath("$.content[0].quelle")
                        .doesNotExist())
                .andExpect(jsonPath("$.content[0].status")
                        .value(VorgangStatus.I.name()))
                // putApiAufgabeUpdate
                .andExpect(jsonPath("$.content[1].projektId")
                        .value(1L))
                .andExpect(jsonPath("$.content[1].nutzerId")
                        .doesNotExist())
                .andExpect(jsonPath("$.content[1].status")
                        .value(VorgangStatus.B.name()))
                // patchApiAufgabeProjekt
                .andExpect(jsonPath("$.content[2].projektId")
                        .value(2L))
                .andExpect(jsonPath("$.content[2].nutzerId")
                        .doesNotExist())
                .andExpect(jsonPath("$.content[2].status")
                        .value(VorgangStatus.B.name()))
                // patchApiAufgabeNutzer
                .andExpect(jsonPath("$.content[3].projektId")
                        .value(2L))
                .andExpect(jsonPath("$.content[3].nutzerId")
                        .exists())
                .andExpect(jsonPath("$.content[3].status")
                        .value(VorgangStatus.B.name()))
                // patchApiAufgabeNutzerNull
                .andExpect(jsonPath("$.content[4].projektId")
                        .value(2L))
                .andExpect(jsonPath("$.content[4].nutzerId")
                        .doesNotExist())
                .andExpect(jsonPath("$.content[4].status")
                        .value(VorgangStatus.B.name()))
                // patchApiAufgabeTitel(Vitamin C)
                .andExpect(jsonPath("$.content[5].titel")
                        .value("Vitamin C"))
                .andExpect(jsonPath("$.content[5].status")
                        .value(VorgangStatus.B.name()))
                // patchApiAufgabeTitel(Aufgabe C)
                .andExpect(jsonPath("$.content[6].titel")
                        .value("Aufgabe C"))
                .andExpect(jsonPath("$.content[6].status")
                        .value(VorgangStatus.B.name()))
                // patchApiAufgabeText
                .andExpect(jsonPath("$.content[7].text")
                        .isNotEmpty())
                .andExpect(jsonPath("$.content[7].status")
                        .value(VorgangStatus.B.name()))
                // patchApiAufgabeStatus(X)
                .andExpect(jsonPath("$.content[8].status")
                        .value(VorgangStatus.X.name()))
                // patchApiAufgabeStatus(I)
                .andExpect(jsonPath("$.content[9].status")
                        .value(VorgangStatus.I.name()))
                // patchApiAufgabeStatus(A)
                .andExpect(jsonPath("$.content[10].status")
                        .value(VorgangStatus.A.name()))
                // patchApiAufgabeStatus(B)
                .andExpect(jsonPath("$.content[11].status")
                        .value(VorgangStatus.B.name()))
                // patchApiAufgabeTermin(2000-01-01)
                .andExpect(jsonPath("$.content[12].termin")
                        .value("2000-01-01"))
                .andExpect(jsonPath("$.content[12].status")
                        .value(VorgangStatus.B.name()))
                // patchApiAufgabeTermin(2031-04-22)
                .andExpect(jsonPath("$.content[13].termin")
                        .value("2031-04-22"))
                .andExpect(jsonPath("$.content[13].status")
                        .value(VorgangStatus.B.name()))
                // patchApiAufgabeTerminserie(J)
                .andExpect(jsonPath("$.content[14].terminserie")
                        .value(Terminserie.J.name()))
                .andExpect(jsonPath("$.content[14].status")
                        .value(VorgangStatus.B.name()))
                // patchApiAufgabeTerminserie(M)
                .andExpect(jsonPath("$.content[15].terminserie")
                        .value(Terminserie.M.name()))
                .andExpect(jsonPath("$.content[15].status")
                        .value(VorgangStatus.B.name()))
                // patchApiAufgabeTerminserie(W)
                .andExpect(jsonPath("$.content[16].terminserie")
                        .value(Terminserie.W.name()))
                .andExpect(jsonPath("$.content[16].status")
                        .value(VorgangStatus.B.name()))
                // patchApiAufgabeTerminserie(T)
                .andExpect(jsonPath("$.content[17].terminserie")
                        .value(Terminserie.T.name()))
                .andExpect(jsonPath("$.content[17].status")
                        .value(VorgangStatus.B.name()))
                // patchApiAufgabeQuelle
                .andExpect(jsonPath("$.content[18].quelle")
                        .exists())
                .andExpect(jsonPath("$.content[18].status")
                        .value(VorgangStatus.B.name()))
                // patchApiAufgabeQuelleNull
                .andExpect(jsonPath("$.content[19].quelle")
                        .doesNotExist())
                .andExpect(jsonPath("$.content[19].status")
                        .value(VorgangStatus.B.name()))
                // patchApiAufgabeStatusTransition
                .andExpect(jsonPath("$.content[20].termin")
                        .value("2031-04-23"))
                .andExpect(jsonPath("$.content[20].status")
                        .value(VorgangStatus.A.name()))
                // deleteApiAufgabe
                .andExpect(jsonPath("$.content[21].id")
                        .value(uuid))
                .andExpect(jsonPath("$.content[21].projektId")
                        .value(2L))
                .andExpect(jsonPath("$.content[21].nutzerId")
                        .exists())
                .andExpect(jsonPath("$.content[21].status")
                        .value(VorgangStatus.A.name()))
                // deleteApiAufgabe
                .andExpect(jsonPath("$.content[22].id")
                        .value(uuid))
                .andExpect(jsonPath("$.content[22].projektId")
                        .value(2L))
                .andExpect(jsonPath("$.content[22].nutzerId")
                        .exists())
                .andExpect(jsonPath("$.content[22].status")
                        .value(VorgangStatus.A.name()))
                .andExpect(jsonPath("$.content[23]")
                        .doesNotExist());
    }

    @Test
    @Order(99)
    @Transactional
    @Rollback(false)
    void cleanup() {
        projektRepository.findAll().forEach(projektRepository::delete);
        nutzerRepository.findAll().forEach(nutzerRepository::delete);
        assertEquals(0, projektRepository.count());
        assertEquals(0, aufgabeRepository.count());
        assertEquals(0, nutzerRepository.count());
    }
}
