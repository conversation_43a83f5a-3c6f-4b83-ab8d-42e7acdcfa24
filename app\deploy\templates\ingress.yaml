apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  namespace: {{.Release.Namespace}}
  name: {{.Release.Name}}-ingress
spec:
  ingressClassName: {{.Values.ingress.class}}
  defaultBackend:
    service:
      name: server-cluster-ip
      port:
        name: backend-http
  rules:
    - host: {{.Values.ingress.host}}
      http:
        # tag::paths[]
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: client-cluster-ip
                port:
                  name: client-http
          - path: /api
            pathType: Prefix
            backend:
              service:
                name: server-cluster-ip
                port:
                  name: backend-http
          - path: /doc
            pathType: Prefix
            backend:
              service:
                name: server-cluster-ip
                port:
                  name: printer-http
          {{ if eq .Values.application.login "" }}
          - path: /jwt
            pathType: Prefix
            backend:
              service:
                name: server-cluster-ip
                port:
                  name: backend-http
          {{ else }}
          - path: /login
            pathType: Prefix
            backend:
              service:
                name: server-cluster-ip
                port:
                  name: backend-http
          - path: /logout
            pathType: Prefix
            backend:
              service:
                name: server-cluster-ip
                port:
                  name: backend-http
          - path: /oauth2
            pathType: Prefix
            backend:
              service:
                name: server-cluster-ip
                port:
                  name: backend-http
          {{ end }}
          - path: /mail
            pathType: Prefix
            backend:
              service:
                name: server-cluster-ip
                port:
                  name: spooler-http
          - path: /print
            pathType: Prefix
            backend:
              service:
                name: server-cluster-ip
                port:
                  name: spooler-http
          - path: /printers
            pathType: Prefix
            backend:
              service:
                name: server-cluster-ip
                port:
                  name: spooler-cups
          - path: /version
            pathType: Prefix
            backend:
              service:
                name: server-cluster-ip
                port:
                  name: backend-http
          - path: /who
            pathType: Prefix
            backend:
              service:
                name: server-cluster-ip
                port:
                  name: backend-http
          - path: /zip
            pathType: Prefix
            backend:
              service:
                name: server-cluster-ip
                port:
                  name: spooler-http
        # end::paths[]
