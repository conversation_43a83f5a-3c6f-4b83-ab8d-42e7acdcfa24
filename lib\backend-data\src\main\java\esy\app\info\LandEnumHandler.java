package esy.app.info;

import esy.api.info.Land;
import lombok.NonNull;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

@Component
public class LandEnumHandler extends EnumHandlerBase {

    public LandEnumHandler(@NonNull final EnumRepository enumRepository) {
        super(enumRepository);
    }

    @Override
    public void onApplicationEvent(@NonNull final ContextRefreshedEvent event) {
        createOrUpdateItem(Land.class.getSimpleName().toLowerCase(), Land.values());
    }
}
