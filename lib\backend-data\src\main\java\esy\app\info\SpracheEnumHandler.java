package esy.app.info;

import esy.api.info.Sprache;
import lombok.NonNull;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

@Component
public class SpracheEnumHandler extends EnumHandlerBase {

    public SpracheEnumHandler(@NonNull final EnumRepository enumRepository) {
        super(enumRepository);
    }

    @Override
    public void onApplicationEvent(@NonNull final ContextRefreshedEvent event) {
        createOrUpdateItem(Sprache.class.getSimpleName().toLowerCase(), Sprache.values());
    }
}
