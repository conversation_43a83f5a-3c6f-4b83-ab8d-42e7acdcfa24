<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.9.xsd">
    <changeSet id="1" author="robert">
        <preConditions onFail="MARK_RAN">
            <not><tableExists tableName="enum_aud" /></not>
        </preConditions>
        <createTable tableName="enum_aud">
            <column name="rev" type="BIGINT">
                <constraints foreignKeyName="fk_enum_aud_revinfo"
                             referencedTableName="revinfo"
                             referencedColumnNames="id"
                             nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
            <column name="id" type="UUID">
                <constraints nullable="true"/>
            </column>
            <column name="art" type="VARCHAR(512)">
                <constraints nullable="true"/>
            </column>
            <column name="code" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="name" type="VARCHAR(512)">
                <constraints nullable="true"/>
            </column>
            <column name="text" type="VARCHAR(512)">
                <constraints nullable="true"/>
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>
