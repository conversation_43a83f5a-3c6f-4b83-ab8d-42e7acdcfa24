package esy.api;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.hibernate.envers.RevisionEntity;
import org.hibernate.envers.RevisionNumber;
import org.hibernate.envers.RevisionTimestamp;

import jakarta.persistence.*;

@Entity
@RevisionEntity
@Table(name = "revinfo")
@SequenceGenerator(name = "revinfo_seq", allocationSize = 1)
@EqualsAndHashCode
public class Revision {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "revinfo_seq")
    @Column(nullable = false, name = "id")
    @RevisionNumber
    @Getter
    private long id;

    @Column(nullable = false, name = "timestamp")
    @RevisionTimestamp
    @Getter
    private long timestamp;
}
