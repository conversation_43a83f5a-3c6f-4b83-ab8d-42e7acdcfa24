{"version": 3, "sources": ["../../node_modules/perfect-freehand/dist/esm/index.mjs"], "sourcesContent": ["function $(e,t,u,x=h=>h){return e*x(.5-t*(.5-u))}function se(e){return[-e[0],-e[1]]}function l(e,t){return[e[0]+t[0],e[1]+t[1]]}function a(e,t){return[e[0]-t[0],e[1]-t[1]]}function b(e,t){return[e[0]*t,e[1]*t]}function he(e,t){return[e[0]/t,e[1]/t]}function R(e){return[e[1],-e[0]]}function B(e,t){return e[0]*t[0]+e[1]*t[1]}function ue(e,t){return e[0]===t[0]&&e[1]===t[1]}function ge(e){return Math.hypot(e[0],e[1])}function de(e){return e[0]*e[0]+e[1]*e[1]}function A(e,t){return de(a(e,t))}function G(e){return he(e,ge(e))}function ie(e,t){return Math.hypot(e[1]-t[1],e[0]-t[0])}function L(e,t,u){let x=Math.sin(u),h=Math.cos(u),y=e[0]-t[0],n=e[1]-t[1],f=y*h-n*x,d=y*x+n*h;return[f+t[0],d+t[1]]}function K(e,t,u){return l(e,b(a(t,e),u))}function ee(e,t,u){return l(e,b(t,u))}var{min:C,PI:xe}=Math,pe=.275,V=xe+1e-4;function ce(e,t={}){let{size:u=16,smoothing:x=.5,thinning:h=.5,simulatePressure:y=!0,easing:n=r=>r,start:f={},end:d={},last:D=!1}=t,{cap:S=!0,easing:j=r=>r*(2-r)}=f,{cap:q=!0,easing:c=r=>--r*r*r+1}=d;if(e.length===0||u<=0)return[];let p=e[e.length-1].runningLength,g=f.taper===!1?0:f.taper===!0?Math.max(u,p):f.taper,T=d.taper===!1?0:d.taper===!0?Math.max(u,p):d.taper,te=Math.pow(u*x,2),_=[],M=[],H=e.slice(0,10).reduce((r,i)=>{let o=i.pressure;if(y){let s=C(1,i.distance/u),W=C(1,1-s);o=C(1,r+(W-r)*(s*pe))}return(r+o)/2},e[0].pressure),m=$(u,h,e[e.length-1].pressure,n),U,X=e[0].vector,z=e[0].point,F=z,O=z,E=F,J=!1;for(let r=0;r<e.length;r++){let{pressure:i}=e[r],{point:o,vector:s,distance:W,runningLength:I}=e[r];if(r<e.length-1&&p-I<3)continue;if(h){if(y){let v=C(1,W/u),Z=C(1,1-v);i=C(1,H+(Z-H)*(v*pe))}m=$(u,h,i,n)}else m=u/2;U===void 0&&(U=m);let le=I<g?j(I/g):1,fe=p-I<T?c((p-I)/T):1;m=Math.max(.01,m*Math.min(le,fe));let re=(r<e.length-1?e[r+1]:e[r]).vector,Y=r<e.length-1?B(s,re):1,be=B(s,X)<0&&!J,ne=Y!==null&&Y<0;if(be||ne){let v=b(R(X),m);for(let Z=1/13,w=0;w<=1;w+=Z)O=L(a(o,v),o,V*w),_.push(O),E=L(l(o,v),o,V*-w),M.push(E);z=O,F=E,ne&&(J=!0);continue}if(J=!1,r===e.length-1){let v=b(R(s),m);_.push(a(o,v)),M.push(l(o,v));continue}let oe=b(R(K(re,s,Y)),m);O=a(o,oe),(r<=1||A(z,O)>te)&&(_.push(O),z=O),E=l(o,oe),(r<=1||A(F,E)>te)&&(M.push(E),F=E),H=i,X=s}let P=e[0].point.slice(0,2),k=e.length>1?e[e.length-1].point.slice(0,2):l(e[0].point,[1,1]),Q=[],N=[];if(e.length===1){if(!(g||T)||D){let r=ee(P,G(R(a(P,k))),-(U||m)),i=[];for(let o=1/13,s=o;s<=1;s+=o)i.push(L(r,P,V*2*s));return i}}else{if(!(g||T&&e.length===1))if(S)for(let i=1/13,o=i;o<=1;o+=i){let s=L(M[0],P,V*o);Q.push(s)}else{let i=a(_[0],M[0]),o=b(i,.5),s=b(i,.51);Q.push(a(P,o),a(P,s),l(P,s),l(P,o))}let r=R(se(e[e.length-1].vector));if(T||g&&e.length===1)N.push(k);else if(q){let i=ee(k,r,m);for(let o=1/29,s=o;s<1;s+=o)N.push(L(i,k,V*3*s))}else N.push(l(k,b(r,m)),l(k,b(r,m*.99)),a(k,b(r,m*.99)),a(k,b(r,m)))}return _.concat(N,M.reverse(),Q)}function me(e,t={}){var q;let{streamline:u=.5,size:x=16,last:h=!1}=t;if(e.length===0)return[];let y=.15+(1-u)*.85,n=Array.isArray(e[0])?e:e.map(({x:c,y:p,pressure:g=.5})=>[c,p,g]);if(n.length===2){let c=n[1];n=n.slice(0,-1);for(let p=1;p<5;p++)n.push(K(n[0],c,p/4))}n.length===1&&(n=[...n,[...l(n[0],[1,1]),...n[0].slice(2)]]);let f=[{point:[n[0][0],n[0][1]],pressure:n[0][2]>=0?n[0][2]:.25,vector:[1,1],distance:0,runningLength:0}],d=!1,D=0,S=f[0],j=n.length-1;for(let c=1;c<n.length;c++){let p=h&&c===j?n[c].slice(0,2):K(S.point,n[c],y);if(ue(S.point,p))continue;let g=ie(p,S.point);if(D+=g,c<j&&!d){if(D<x)continue;d=!0}S={point:p,pressure:n[c][2]>=0?n[c][2]:.5,vector:G(a(S.point,p)),distance:g,runningLength:D},f.push(S)}return f[0].vector=((q=f[1])==null?void 0:q.vector)||[0,0],f}function ae(e,t={}){return ce(me(e,t),t)}var _e=ae;export{_e as default,ae as getStroke,ce as getStrokeOutlinePoints,me as getStrokePoints};\n"], "mappings": ";;;AAAA,SAAS,EAAE,GAAE,GAAE,GAAE,IAAE,OAAG,GAAE;AAAC,SAAO,IAAE,EAAE,MAAG,KAAG,MAAG,EAAE;AAAC;AAAC,SAAS,GAAG,GAAE;AAAC,SAAM,CAAC,CAAC,EAAE,CAAC,GAAE,CAAC,EAAE,CAAC,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,SAAM,CAAC,EAAE,CAAC,IAAE,EAAE,CAAC,GAAE,EAAE,CAAC,IAAE,EAAE,CAAC,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,SAAM,CAAC,EAAE,CAAC,IAAE,EAAE,CAAC,GAAE,EAAE,CAAC,IAAE,EAAE,CAAC,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,SAAM,CAAC,EAAE,CAAC,IAAE,GAAE,EAAE,CAAC,IAAE,CAAC;AAAC;AAAC,SAAS,GAAG,GAAE,GAAE;AAAC,SAAM,CAAC,EAAE,CAAC,IAAE,GAAE,EAAE,CAAC,IAAE,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,SAAM,CAAC,EAAE,CAAC,GAAE,CAAC,EAAE,CAAC,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,SAAO,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC;AAAC;AAAC,SAAS,GAAG,GAAE,GAAE;AAAC,SAAO,EAAE,CAAC,MAAI,EAAE,CAAC,KAAG,EAAE,CAAC,MAAI,EAAE,CAAC;AAAC;AAAC,SAAS,GAAG,GAAE;AAAC,SAAO,KAAK,MAAM,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAC;AAAC,SAAS,GAAG,GAAE;AAAC,SAAO,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,SAAO,GAAG,EAAE,GAAE,CAAC,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,SAAO,GAAG,GAAE,GAAG,CAAC,CAAC;AAAC;AAAC,SAAS,GAAG,GAAE,GAAE;AAAC,SAAO,KAAK,MAAM,EAAE,CAAC,IAAE,EAAE,CAAC,GAAE,EAAE,CAAC,IAAE,EAAE,CAAC,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE,GAAE;AAAC,MAAI,IAAE,KAAK,IAAI,CAAC,GAAE,IAAE,KAAK,IAAI,CAAC,GAAE,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,GAAE,IAAE,IAAE,IAAE,IAAE,GAAE,IAAE,IAAE,IAAE,IAAE;AAAE,SAAM,CAAC,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE,GAAE;AAAC,SAAO,EAAE,GAAE,EAAE,EAAE,GAAE,CAAC,GAAE,CAAC,CAAC;AAAC;AAAC,SAAS,GAAG,GAAE,GAAE,GAAE;AAAC,SAAO,EAAE,GAAE,EAAE,GAAE,CAAC,CAAC;AAAC;AAAC,IAAG,EAAC,KAAI,GAAE,IAAG,GAAE,IAAE;AAAjB,IAAsB,KAAG;AAAzB,IAA8B,IAAE,KAAG;AAAK,SAAS,GAAG,GAAE,IAAE,CAAC,GAAE;AAAC,MAAG,EAAC,MAAK,IAAE,IAAG,WAAU,IAAE,KAAG,UAAS,IAAE,KAAG,kBAAiB,IAAE,MAAG,QAAO,IAAE,OAAG,GAAE,OAAM,IAAE,CAAC,GAAE,KAAI,IAAE,CAAC,GAAE,MAAK,IAAE,MAAE,IAAE,GAAE,EAAC,KAAI,IAAE,MAAG,QAAO,IAAE,OAAG,KAAG,IAAE,GAAE,IAAE,GAAE,EAAC,KAAI,IAAE,MAAG,QAAO,IAAE,OAAG,EAAE,IAAE,IAAE,IAAE,EAAC,IAAE;AAAE,MAAG,EAAE,WAAS,KAAG,KAAG,EAAE,QAAM,CAAC;AAAE,MAAI,IAAE,EAAE,EAAE,SAAO,CAAC,EAAE,eAAc,IAAE,EAAE,UAAQ,QAAG,IAAE,EAAE,UAAQ,OAAG,KAAK,IAAI,GAAE,CAAC,IAAE,EAAE,OAAM,IAAE,EAAE,UAAQ,QAAG,IAAE,EAAE,UAAQ,OAAG,KAAK,IAAI,GAAE,CAAC,IAAE,EAAE,OAAM,KAAG,KAAK,IAAI,IAAE,GAAE,CAAC,GAAE,IAAE,CAAC,GAAE,IAAE,CAAC,GAAE,IAAE,EAAE,MAAM,GAAE,EAAE,EAAE,OAAO,CAAC,GAAE,MAAI;AAAC,QAAI,IAAE,EAAE;AAAS,QAAG,GAAE;AAAC,UAAI,IAAE,EAAE,GAAE,EAAE,WAAS,CAAC,GAAE,IAAE,EAAE,GAAE,IAAE,CAAC;AAAE,UAAE,EAAE,GAAE,KAAG,IAAE,MAAI,IAAE,GAAG;AAAA,IAAC;AAAC,YAAO,IAAE,KAAG;AAAA,EAAC,GAAE,EAAE,CAAC,EAAE,QAAQ,GAAE,IAAE,EAAE,GAAE,GAAE,EAAE,EAAE,SAAO,CAAC,EAAE,UAAS,CAAC,GAAE,GAAE,IAAE,EAAE,CAAC,EAAE,QAAO,IAAE,EAAE,CAAC,EAAE,OAAM,IAAE,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE;AAAG,WAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,KAAI;AAAC,QAAG,EAAC,UAAS,EAAC,IAAE,EAAE,CAAC,GAAE,EAAC,OAAM,GAAE,QAAO,GAAE,UAAS,GAAE,eAAc,EAAC,IAAE,EAAE,CAAC;AAAE,QAAG,IAAE,EAAE,SAAO,KAAG,IAAE,IAAE,EAAE;AAAS,QAAG,GAAE;AAAC,UAAG,GAAE;AAAC,YAAI,IAAE,EAAE,GAAE,IAAE,CAAC,GAAE,IAAE,EAAE,GAAE,IAAE,CAAC;AAAE,YAAE,EAAE,GAAE,KAAG,IAAE,MAAI,IAAE,GAAG;AAAA,MAAC;AAAC,UAAE,EAAE,GAAE,GAAE,GAAE,CAAC;AAAA,IAAC,MAAM,KAAE,IAAE;AAAE,UAAI,WAAS,IAAE;AAAG,QAAI,KAAG,IAAE,IAAE,EAAE,IAAE,CAAC,IAAE,GAAE,KAAG,IAAE,IAAE,IAAE,GAAG,IAAE,KAAG,CAAC,IAAE;AAAE,QAAE,KAAK,IAAI,MAAI,IAAE,KAAK,IAAI,IAAG,EAAE,CAAC;AAAE,QAAI,MAAI,IAAE,EAAE,SAAO,IAAE,EAAE,IAAE,CAAC,IAAE,EAAE,CAAC,GAAG,QAAO,IAAE,IAAE,EAAE,SAAO,IAAE,EAAE,GAAE,EAAE,IAAE,GAAE,KAAG,EAAE,GAAE,CAAC,IAAE,KAAG,CAAC,GAAE,KAAG,MAAI,QAAM,IAAE;AAAE,QAAG,MAAI,IAAG;AAAC,UAAI,IAAE,EAAE,EAAE,CAAC,GAAE,CAAC;AAAE,eAAQ,IAAE,IAAE,IAAG,IAAE,GAAE,KAAG,GAAE,KAAG,EAAE,KAAE,EAAE,EAAE,GAAE,CAAC,GAAE,GAAE,IAAE,CAAC,GAAE,EAAE,KAAK,CAAC,GAAE,IAAE,EAAE,EAAE,GAAE,CAAC,GAAE,GAAE,IAAE,CAAC,CAAC,GAAE,EAAE,KAAK,CAAC;AAAE,UAAE,GAAE,IAAE,GAAE,OAAK,IAAE;AAAI;AAAA,IAAQ;AAAC,QAAG,IAAE,OAAG,MAAI,EAAE,SAAO,GAAE;AAAC,UAAI,IAAE,EAAE,EAAE,CAAC,GAAE,CAAC;AAAE,QAAE,KAAK,EAAE,GAAE,CAAC,CAAC,GAAE,EAAE,KAAK,EAAE,GAAE,CAAC,CAAC;AAAE;AAAA,IAAQ;AAAC,QAAI,KAAG,EAAE,EAAE,EAAE,IAAG,GAAE,CAAC,CAAC,GAAE,CAAC;AAAE,QAAE,EAAE,GAAE,EAAE,IAAG,KAAG,KAAG,EAAE,GAAE,CAAC,IAAE,QAAM,EAAE,KAAK,CAAC,GAAE,IAAE,IAAG,IAAE,EAAE,GAAE,EAAE,IAAG,KAAG,KAAG,EAAE,GAAE,CAAC,IAAE,QAAM,EAAE,KAAK,CAAC,GAAE,IAAE,IAAG,IAAE,GAAE,IAAE;AAAA,EAAC;AAAC,MAAI,IAAE,EAAE,CAAC,EAAE,MAAM,MAAM,GAAE,CAAC,GAAE,IAAE,EAAE,SAAO,IAAE,EAAE,EAAE,SAAO,CAAC,EAAE,MAAM,MAAM,GAAE,CAAC,IAAE,EAAE,EAAE,CAAC,EAAE,OAAM,CAAC,GAAE,CAAC,CAAC,GAAE,IAAE,CAAC,GAAE,IAAE,CAAC;AAAE,MAAG,EAAE,WAAS,GAAE;AAAC,QAAG,EAAE,KAAG,MAAI,GAAE;AAAC,UAAI,IAAE,GAAG,GAAE,EAAE,EAAE,EAAE,GAAE,CAAC,CAAC,CAAC,GAAE,EAAE,KAAG,EAAE,GAAE,IAAE,CAAC;AAAE,eAAQ,IAAE,IAAE,IAAG,IAAE,GAAE,KAAG,GAAE,KAAG,EAAE,GAAE,KAAK,EAAE,GAAE,GAAE,IAAE,IAAE,CAAC,CAAC;AAAE,aAAO;AAAA,IAAC;AAAA,EAAC,OAAK;AAAC,QAAG,EAAE,KAAG,KAAG,EAAE,WAAS,GAAG,KAAG,EAAE,UAAQ,IAAE,IAAE,IAAG,IAAE,GAAE,KAAG,GAAE,KAAG,GAAE;AAAC,UAAI,IAAE,EAAE,EAAE,CAAC,GAAE,GAAE,IAAE,CAAC;AAAE,QAAE,KAAK,CAAC;AAAA,IAAC;AAAA,SAAK;AAAC,UAAI,IAAE,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,IAAE,EAAE,GAAE,IAAG;AAAE,QAAE,KAAK,EAAE,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,CAAC;AAAA,IAAC;AAAC,QAAI,IAAE,EAAE,GAAG,EAAE,EAAE,SAAO,CAAC,EAAE,MAAM,CAAC;AAAE,QAAG,KAAG,KAAG,EAAE,WAAS,EAAE,GAAE,KAAK,CAAC;AAAA,aAAU,GAAE;AAAC,UAAI,IAAE,GAAG,GAAE,GAAE,CAAC;AAAE,eAAQ,IAAE,IAAE,IAAG,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE,GAAE,KAAK,EAAE,GAAE,GAAE,IAAE,IAAE,CAAC,CAAC;AAAA,IAAC,MAAM,GAAE,KAAK,EAAE,GAAE,EAAE,GAAE,CAAC,CAAC,GAAE,EAAE,GAAE,EAAE,GAAE,IAAE,IAAG,CAAC,GAAE,EAAE,GAAE,EAAE,GAAE,IAAE,IAAG,CAAC,GAAE,EAAE,GAAE,EAAE,GAAE,CAAC,CAAC,CAAC;AAAA,EAAC;AAAC,SAAO,EAAE,OAAO,GAAE,EAAE,QAAQ,GAAE,CAAC;AAAC;AAAC,SAAS,GAAG,GAAE,IAAE,CAAC,GAAE;AAAC,MAAI;AAAE,MAAG,EAAC,YAAW,IAAE,KAAG,MAAK,IAAE,IAAG,MAAK,IAAE,MAAE,IAAE;AAAE,MAAG,EAAE,WAAS,EAAE,QAAM,CAAC;AAAE,MAAI,IAAE,QAAK,IAAE,KAAG,MAAI,IAAE,MAAM,QAAQ,EAAE,CAAC,CAAC,IAAE,IAAE,EAAE,IAAI,CAAC,EAAC,GAAE,GAAE,GAAE,GAAE,UAAS,IAAE,IAAE,MAAI,CAAC,GAAE,GAAE,CAAC,CAAC;AAAE,MAAG,EAAE,WAAS,GAAE;AAAC,QAAI,IAAE,EAAE,CAAC;AAAE,QAAE,EAAE,MAAM,GAAE,EAAE;AAAE,aAAQ,IAAE,GAAE,IAAE,GAAE,IAAI,GAAE,KAAK,EAAE,EAAE,CAAC,GAAE,GAAE,IAAE,CAAC,CAAC;AAAA,EAAC;AAAC,IAAE,WAAS,MAAI,IAAE,CAAC,GAAG,GAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAE,CAAC,GAAE,CAAC,CAAC,GAAE,GAAG,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;AAAG,MAAI,IAAE,CAAC,EAAC,OAAM,CAAC,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAE,UAAS,EAAE,CAAC,EAAE,CAAC,KAAG,IAAE,EAAE,CAAC,EAAE,CAAC,IAAE,MAAI,QAAO,CAAC,GAAE,CAAC,GAAE,UAAS,GAAE,eAAc,EAAC,CAAC,GAAE,IAAE,OAAG,IAAE,GAAE,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,SAAO;AAAE,WAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,KAAI;AAAC,QAAI,IAAE,KAAG,MAAI,IAAE,EAAE,CAAC,EAAE,MAAM,GAAE,CAAC,IAAE,EAAE,EAAE,OAAM,EAAE,CAAC,GAAE,CAAC;AAAE,QAAG,GAAG,EAAE,OAAM,CAAC,EAAE;AAAS,QAAI,IAAE,GAAG,GAAE,EAAE,KAAK;AAAE,QAAG,KAAG,GAAE,IAAE,KAAG,CAAC,GAAE;AAAC,UAAG,IAAE,EAAE;AAAS,UAAE;AAAA,IAAE;AAAC,QAAE,EAAC,OAAM,GAAE,UAAS,EAAE,CAAC,EAAE,CAAC,KAAG,IAAE,EAAE,CAAC,EAAE,CAAC,IAAE,KAAG,QAAO,EAAE,EAAE,EAAE,OAAM,CAAC,CAAC,GAAE,UAAS,GAAE,eAAc,EAAC,GAAE,EAAE,KAAK,CAAC;AAAA,EAAC;AAAC,SAAO,EAAE,CAAC,EAAE,WAAS,IAAE,EAAE,CAAC,MAAI,OAAK,SAAO,EAAE,WAAS,CAAC,GAAE,CAAC,GAAE;AAAC;AAAC,SAAS,GAAG,GAAE,IAAE,CAAC,GAAE;AAAC,SAAO,GAAG,GAAG,GAAE,CAAC,GAAE,CAAC;AAAC;AAAC,IAAI,KAAG;", "names": []}