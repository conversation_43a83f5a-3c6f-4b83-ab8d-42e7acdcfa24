package esy.api.info;

import com.fasterxml.jackson.annotation.JsonProperty;
import esy.json.JsonJpaItem;
import esy.json.JsonMapper;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NonNull;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;

@Embeddable
@EqualsAndHashCode
public class TextItem implements JsonJpaItem<String>  {

    // tag::titel[]
    @Column(name = "titel")
    @Getter
    @JsonProperty
    private String titel;
    // end::titel[]

    // tag::text[]
    @Column(name = "text")
    @Getter
    @JsonProperty
    private String text;
    // end::text[]

    // tag::newNoArgs[]
    TextItem() {
        this.titel = null;
        this.text = "";
    }
    // end::newNoArgs[]

    // tag::newAllArgs[]
    TextItem(@NonNull final String titel, @NonNull final String text) {
        this.titel = titel;
        this.text = text;
    }
    // end::newAllArgs[]

    // tag::value[]
    @Override
    @JsonProperty
    public String getValue() {
        return titel;
    }
    // end::value[]

    // tag::toString[]
    @Override
    public String toString() {
        return getValue() == null ? "" : titel;
    }
    // end::toString[]

    // tag::writeJson[]
    public String writeJson() {
        return new JsonMapper().writeJson(this);
    }
    // end::writeJson[]

    // tag::parseJson[]
    public static TextItem parseJson(@NonNull final String json) {
        return new JsonMapper().parseJson(json, TextItem.class);
    }
    // end::parseJson[]
}
