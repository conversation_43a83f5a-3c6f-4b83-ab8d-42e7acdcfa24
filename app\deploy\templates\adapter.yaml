apiVersion: batch/v1
kind: Job
metadata:
  namespace: {{.Release.Namespace}}
  name: adapter-job-{{ randAlphaNum 5 | lower }}
spec:
  parallelism: 1
  completions: 1
  backoffLimit: 10
  template:
    metadata:
      labels:
        release: {{.Release.Name}}
        version: {{.Chart.Version}}
        chart: {{.Chart.Name}}
        component: adapter
    spec:
{{ if .Values.image.secret }}
      imagePullSecrets:
        - name: {{ .Values.image.secret }}
{{ end }}
      restartPolicy: OnFailure
      containers:
        - name: adapter-container
          image: "{{.Values.image.repository}}/{{.Values.image.adapterImageName}}:{{.Values.image.tag}}"
          imagePullPolicy: IfNotPresent
          # tag::resources[]
          resources:
            requests:
              cpu: {{.Values.image.adapterCpu}}
              memory: {{.Values.image.adapterMemory}}
            limits:
              #cpu: no limit on single node
              memory: {{.Values.image.adapterMemory}}
          # tag::resources[]
          # tag::env[]
          env:
            - name: ADAPTER_DELAY_SECONDS
              value: "300"
            - name: BACKEND_URL
              value: "http://server-cluster-ip:{{.Values.image.backendPort}}"
            - name: BACKEND_LOGIN
              value: "bruckbauer"
            - name: BACKEND_EMAIL
              value: "<EMAIL>"
            - name: PRINTER_URL
              value: "http://server-cluster-ip:{{.Values.image.printerPort}}"
            - name: SPOOLER_URL
              value: "http://server-cluster-ip:{{.Values.image.spoolerPort}}"
            - name: TYPICODE_URL
              value: "https://jsonplaceholder.typicode.com"
          # end::env[]
