package esy.app.zeit;

import com.microsoft.playwright.Playwright;
import com.microsoft.playwright.options.RequestOptions;
import esy.api.team.Nutzer;
import esy.api.zeit.Abwesenheit;
import esy.api.zeit.Anwesenheit;
import esy.api.zeit.Einsatz;
import esy.api.zeit.Einsatztag;
import esy.app.PlaywrightApiAssertionBase;
import esy.app.PlaywrightApiTestEnv;
import esy.json.JsonMapper;
import lombok.NonNull;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.UUID;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.jupiter.api.Assertions.*;

@SuppressWarnings("java:S1192") // duplicating literals is ok
public class EinsatzPlaywrightApiAssertion extends PlaywrightApiAssertionBase {

    public EinsatzPlaywrightApiAssertion(@NonNull final Playwright playwright, @NonNull final PlaywrightApiTestEnv env) {
        super(playwright, env);
        login();
    }

    public void assertEinsatz() {
        final var nutzer1 = createRandomNutzer();

        final var datum1 = LocalDate.of(2020, 4, 22);
        final var einsatzId1 = doWithApi(
                (api) -> api.post("/api/einsatz", RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"datum\":\"%s\",\"nutzerId\":\"%s\",\"planStart\":\"07:30\",\"planDauer\":\"240\",\"echtStart\":\"07:33\",\"echtDauer\":\"242\"}",
                                datum1, nutzer1.getId()))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.CREATED.value()));
                    final var json = Einsatz.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(datum1, json.getDatum());
                    assertEquals(nutzer1.getId(), json.getNutzerId());
                    assertEquals(LocalTime.of(7, 30), json.getPlanStart());
                    assertEquals(LocalTime.of(7, 33), json.getEchtStart());
                    assertEquals(240L, json.getPlanDauer());
                    assertEquals(242L, json.getEchtDauer());
                    return json.getId();
                });

        final var datum2 = LocalDate.of(2020, 4, 23);
        final var einsatzId2 = doWithApi(
                (api) -> api.put("/api/einsatz/" + UUID.randomUUID(), RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"datum\":\"%s\",\"nutzerId\":\"%s\"}",
                                datum2, nutzer1.getId()))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.CREATED.value()));
                    final var json = Einsatz.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(datum2, json.getDatum());
                    assertEquals(nutzer1.getId(), json.getNutzerId());
                    assertEquals(LocalTime.of(9, 0), json.getPlanStart());
                    assertNull(json.getEchtStart());
                    assertEquals(540L, json.getPlanDauer());
                    assertNull(json.getEchtDauer());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.put("/api/einsatz/" + einsatzId2, RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"datum\":\"%s\",\"nutzerId\":\"%s\",\"planStart\":\"07:30\",\"planDauer\":\"540\"}",
                                datum2, nutzer1.getId()))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Einsatz.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(datum2, json.getDatum());
                    assertEquals(nutzer1.getId(), json.getNutzerId());
                    assertEquals(LocalTime.of(7, 30), json.getPlanStart());
                    assertNull(json.getEchtStart());
                    assertEquals(540L, json.getPlanDauer());
                    assertNull(json.getEchtDauer());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.patch("/api/einsatz/" + einsatzId2, RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, "application/merge-patch+json")
                        .setData("{\"planDauer\":\"240\"}")),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Einsatz.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(LocalTime.of(7, 30), json.getPlanStart());
                    assertNull(json.getEchtStart());
                    assertEquals(240L, json.getPlanDauer());
                    assertNull(json.getEchtDauer());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.get("/api/einsatz/search/findAllByNutzerAndJahrAndMonat", RequestOptions.create()
                        .setQueryParam("nutzerId", nutzer1.getId().toString())
                        .setQueryParam("jahr", datum1.getYear())
                        .setQueryParam("monat", datum1.getMonthValue())),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var jsonReader = new JsonMapper().parseJsonPath(res.text());
                    final var allAbwesenheitId = jsonReader.readContentId();
                    assertTrue(allAbwesenheitId.contains(einsatzId1.toString()));
                    assertTrue(allAbwesenheitId.contains(einsatzId2.toString()));
                    return allAbwesenheitId;
                });

        doWithApi(
                (api) -> api.get("/api/einsatz/search/findAllByNutzerAndDatumBetween", RequestOptions.create()
                        .setQueryParam("nutzerId", nutzer1.getId().toString())
                        .setQueryParam("von", datum1.getYear() + "-04-21")
                        .setQueryParam("bis", datum1.getYear() + "-04-22")),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var jsonReader = new JsonMapper().parseJsonPath(res.text());
                    final var allAbwesenheitId = jsonReader.readContentId();
                    assertTrue(allAbwesenheitId.contains(einsatzId1.toString()));
                    assertFalse(allAbwesenheitId.contains(einsatzId2.toString()));
                    return allAbwesenheitId;
                });

        doWithApi(
                (api) -> api.delete("/api/einsatz/" + einsatzId1),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Einsatz.parseJson(res.text());
                    assertEquals(einsatzId1, json.getId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.delete("/api/einsatz/" + einsatzId2),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Einsatz.parseJson(res.text());
                    assertEquals(einsatzId2, json.getId());
                    return json.getId();
                });
    }

    public void assertEinsatzBesitzer() {
        final var nutzer1 = createRandomNutzer();
        final var nutzer2 = createRandomNutzer();

        final var datum1 = LocalDate.of(2020, 4, 22);
        final var einsatzId1 = doWithApi(
                (api) -> api.post("/api/einsatz", RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"datum\":\"%s\",\"nutzerId\":\"%s\",\"planStart\":\"07:30\",\"echtStart\":\"07:33\",\"planDauer\":\"240\",\"echtDauer\":\"242\"}",
                                datum1, nutzer1.getId()))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.CREATED.value()));
                    final var json = Einsatz.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(datum1, json.getDatum());
                    assertEquals(nutzer1.getId(), json.getNutzerId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.get("/api/einsatz/" + einsatzId1 + "/nutzer"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Nutzer.parseJson(res.text());
                    assertEquals(nutzer1.getId(), json.getId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.patch("/api/einsatz/" + einsatzId1, RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, "application/merge-patch+json")
                        .setData(String.format("{\"nutzerId\":\"%s\"}",
                                nutzer2.getId()))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Einsatz.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(nutzer2.getId(), json.getNutzerId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.get("/api/einsatz/" + einsatzId1 + "/nutzer"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Nutzer.parseJson(res.text());
                    assertEquals(nutzer2.getId(), json.getId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.delete("/api/einsatz/" + einsatzId1),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Einsatz.parseJson(res.text());
                    assertEquals(einsatzId1, json.getId());
                    return json.getId();
                });
    }

    public void assertEinsatzAnwesenheit() {
        final var nutzer1 = createRandomNutzer();

        final var datum1 = LocalDate.of(2020, 4, 22);
        final var anwesenheitId = doWithApi(
                (api) -> api.post("/api/anwesenheit", RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"einsatztag\":\"%s\",\"nutzerId\":\"%s\",\"planStart\":\"07:30\",\"planDauer\":\"240\"}",
                                Einsatztag.valueOf(datum1), nutzer1.getId()))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.CREATED.value()));
                    final var json = Anwesenheit.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(nutzer1.getId(), json.getNutzerId());
                    return json.getId();
                });

        final var einsatzId1 = UUID.randomUUID();
        assertEquals(einsatzId1, doWithApi(
                (api) -> api.put("/api/einsatz/" + einsatzId1, RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"datum\":\"%s\",\"nutzerId\":\"%s\"}",
                                datum1, nutzer1.getId()))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.CREATED.value()));
                    final var json = Einsatz.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(datum1, json.getDatum());
                    assertEquals(nutzer1.getId(), json.getNutzerId());
                    assertEquals(LocalTime.of(7, 30), json.getPlanStart());
                    assertNull(json.getEchtStart());
                    assertEquals(240L, json.getPlanDauer());
                    assertNull(json.getEchtDauer());
                    return json.getId();
                }));

        doWithApi(
                (api) -> api.delete("/api/anwesenheit/" + anwesenheitId),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Anwesenheit.parseJson(res.text());
                    assertEquals(anwesenheitId, json.getId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.delete("/api/einsatz/" + einsatzId1),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Einsatz.parseJson(res.text());
                    assertEquals(einsatzId1, json.getId());
                    return json.getId();
                });
    }

    public void assertEinsatzAbwesenheit() {
        final var nutzer1 = createRandomNutzer();

        final var datum1 = LocalDate.of(2020, 4, 22);
        final var abwesenheitId = doWithApi(
                (api) -> api.post("/api/abwesenheit", RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"datum\":\"%s\",\"nutzerId\":\"%s\"}",
                                datum1, nutzer1.getId()))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.CREATED.value()));
                    final var json = Abwesenheit.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(nutzer1.getId(), json.getNutzerId());
                    return json.getId();
                });

        final var einsatzId1 = UUID.randomUUID();
        assertNull(doWithApi(
                (api) -> api.put("/api/einsatz/" + einsatzId1, RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"datum\":\"%s\",\"nutzerId\":\"%s\"}",
                                datum1, nutzer1.getId()))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.CONFLICT.value()));
                    return null;
                }));

        doWithApi(
                (api) -> api.delete("/api/abwesenheit/" + abwesenheitId),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Anwesenheit.parseJson(res.text());
                    assertEquals(abwesenheitId, json.getId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.delete("/api/einsatz/" + einsatzId1),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.NOT_FOUND.value()));
                    return null;
                });
    }
}
