package esy;

import esy.util.EnvLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import scs.ScsBackendRoot;

@Slf4j
@SpringBootApplication
@ComponentScan(basePackages = {EsyBackendRoot.PACKAGE_NAME, ScsBackendRoot.PACKAGE_NAME})
@Import(EsyBackendTestSet.class)
public class EsyBackendRunner {

    public static void main(final String[] args) {
        new EnvLogger("GIT", "JAV<PERSON>", "JDK", "JIRA", "OAUTH2", "SERVER", "SPRING").log(log);
        final SpringApplicationBuilder builder = new SpringApplicationBuilder(EsyBackendRunner.class);
        builder.run(args);
    }
}
