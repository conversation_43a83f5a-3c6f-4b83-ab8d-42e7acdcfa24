package esy.app.team;

import esy.EsyBackendAware;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(EsyBackendAware.API_PATH)
@RequiredArgsConstructor
public class NutzerKatalogAdocController {

    private final NutzerKatalogAdocRenderer nutzerKatalogAdocRenderer;

    @GetMapping(
            value = {"/nutzer/katalog", "/nutzer/katalog.adoc"},
            produces = "text/asciidoc;charset=UTF-8"
    )
    public String getNutzerKatalog() {
        return nutzerKatalogAdocRenderer.render();
    }
}
