package esy.app.info;

import com.microsoft.playwright.Playwright;
import com.microsoft.playwright.options.RequestOptions;
import esy.api.info.Ping;
import esy.app.PlaywrightApiAssertionBase;
import esy.app.PlaywrightApiTestEnv;
import lombok.NonNull;
import org.hamcrest.Matchers;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;

import java.util.UUID;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.jupiter.api.Assertions.*;

@SuppressWarnings("java:S1192") // duplicating literals is ok
public class PingPlaywrightApiAssertion extends PlaywrightApiAssertionBase {

    public PingPlaywrightApiAssertion(@NonNull final Playwright playwright, @NonNull final PlaywrightApiTestEnv env) {
        super(playwright, env);
        login();
    }

    public void assertPing(@NonNull final String text) {
        final var pingId1 = doWithApi(
                (api) -> api.post("/api/ping", RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"text\":\"%s\"}", text))),
                (res) -> {
                    assertThat(res.status(), Matchers.equalTo(HttpStatus.CREATED.value()));
                    final var json = Ping.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(1L, json.getCount());
                    assertEquals(text, json.getText());
                    assertFalse(json.getUri().isPresent());
                    return json.getId();
                });

        final var pingId2 = doWithApi(
                (api) -> api.post("/api/ping", RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"text\":\"%s\"}", text))),
                (res) -> {
                    assertThat(res.status(), Matchers.equalTo(HttpStatus.CREATED.value()));
                    final var json = Ping.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertNotEquals(pingId1, json.getId());
                    assertEquals(1L, json.getCount());
                    assertEquals(text, json.getText());
                    assertFalse(json.getUri().isPresent());
                    return json.getId();
                });

        final var pingId3 = doWithApi(
                (api) -> api.put("/api/ping/" + UUID.randomUUID(), RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"text\":\"%s\"}", text))),
                (res) -> {
                    assertThat(res.status(), Matchers.equalTo(HttpStatus.CREATED.value()));
                    final var json = Ping.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertNotEquals(pingId1, json.getId());
                    assertNotEquals(pingId2, json.getId());
                    assertEquals(1L, json.getCount());
                    assertEquals(text, json.getText());
                    assertFalse(json.getUri().isPresent());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.put("/api/ping/" + pingId3, RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"text\":\"%s\"}", text))),
                (res) -> {
                    assertThat(res.status(), Matchers.equalTo(HttpStatus.OK.value()));
                    final var json = Ping.parseJson(res.text());
                    assertEquals(pingId3, json.getId());
                    assertEquals(2L, json.getCount());
                    assertEquals(text, json.getText());
                    assertFalse(json.getUri().isPresent());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.put("/api/ping/" + pingId3, RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"text\":\"%s\"}", text.toUpperCase()))),
                (res) -> {
                    assertThat(res.status(), Matchers.equalTo(HttpStatus.OK.value()));
                    final var json = Ping.parseJson(res.text());
                    assertEquals(pingId3, json.getId());
                    assertEquals(3L, json.getCount());
                    assertNotEquals(text, json.getText());
                    assertFalse(json.getUri().isPresent());
                    return json.getId();
                });

        final var uri = "http://a.de";
        doWithApi(
                (api) -> api.put("/api/ping/" + pingId3, RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"text\":\"%s\",\"uri\":\"%s\"}", text, uri))),
                (res) -> {
                    assertThat(res.status(), Matchers.equalTo(HttpStatus.OK.value()));
                    final var json = Ping.parseJson(res.text());
                    assertEquals(pingId3, json.getId());
                    assertEquals(4L, json.getCount());
                    assertEquals(text, json.getText());
                    assertEquals(uri, json.getUri().orElseThrow().toString());
                    return json.getId();
                });
    }
}
