package esy.app.plan;

import com.microsoft.playwright.Playwright;
import com.microsoft.playwright.options.RequestOptions;
import esy.api.plan.Projekt;
import esy.api.team.Nutzer;
import esy.app.PlaywrightApiAssertionBase;
import esy.app.PlaywrightApiTestEnv;
import esy.json.JsonMapper;
import lombok.NonNull;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.jupiter.api.Assertions.*;

@SuppressWarnings("java:S1192") // duplicating literals is ok
public class ProjektPlaywrightApiAssertion extends PlaywrightApiAssertionBase {

    public ProjektPlaywrightApiAssertion(@NonNull final Playwright playwright, @NonNull final PlaywrightApiTestEnv env) {
        super(playwright, env);
        login();
    }

    public void assertProjekt() {
        final var projektName1 = "Projekt A";
        final var projektId1 = doWithApi(
                (api) -> api.post("/api/projekt", RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"name\":\"%s\",\"aktiv\":\"false\"}",
                                projektName1))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.CREATED.value()));
                    final var json = Projekt.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(projektName1, json.getName());
                    assertFalse(json.isAktiv());
                    assertNull(json.getBesitzerId());
                    assertEquals("", json.getSprache());
                    return json.getId();
                });

        final var projektName2 = "Projekt B";
        final var projektId2 = doWithApi(
                (api) -> api.post("/api/projekt", RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"name\":\"%s\",\"aktiv\":\"false\"}",
                                projektName2))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.CREATED.value()));
                    final var json = Projekt.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertNotEquals(projektId1, json.getId());
                    assertEquals(projektName2, json.getName());
                    assertFalse(json.isAktiv());
                    assertNull(json.getBesitzerId());
                    assertEquals("", json.getSprache());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.put("/api/projekt/" + projektId2, RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"name\":\"%s\",\"aktiv\":\"true\"}",
                                projektName2))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Projekt.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(projektName2, json.getName());
                    assertTrue(json.isAktiv());
                    assertNull(json.getBesitzerId());
                    assertEquals("", json.getSprache());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.patch("/api/projekt/" + projektId2, RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, "application/merge-patch+json")
                        .setData("{\"sprache\":\"DE\"}")),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Projekt.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals("DE", json.getSprache());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.get("/api/projekt"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var jsonReader = new JsonMapper().parseJsonPath(res.text());
                    assertNotNull(jsonReader.read("$.page"));
                    final var allGruppeName = jsonReader.readContentField("name");
                    assertTrue(allGruppeName.contains(projektName1));
                    assertTrue(allGruppeName.contains(projektName2));
                    final var allGruppeId = jsonReader.readContentId();
                    assertTrue(allGruppeId.contains(projektId1.toString()));
                    assertTrue(allGruppeId.contains(projektId2.toString()));
                    return allGruppeId;
                });

        doWithApi(
                (api) -> api.get("/api/projekt/" + projektId1),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Projekt.parseJson(res.text());
                    assertEquals(projektId1, json.getId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.get("/api/projekt/search/findByName", RequestOptions.create()
                        .setQueryParam("name", projektName1)),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Projekt.parseJson(res.text());
                    assertEquals(projektId1, json.getId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.delete("/api/projekt/" + projektId1),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Projekt.parseJson(res.text());
                    assertEquals(projektId1, json.getId());
                    assertEquals(projektName1, json.getName());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.delete("/api/projekt/" + projektId2),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Projekt.parseJson(res.text());
                    assertEquals(projektId2, json.getId());
                    assertEquals(projektName2, json.getName());
                    return json.getId();
                });
    }

    public void assertProjektBesitzer() {
        final var nutzer1 = createRandomNutzer();
        final var nutzer2 = createRandomNutzer();
        final var projekt = createRandomProjekt();
        assertNull(projekt.getBesitzerId());

        doWithApi(
                (api) -> api.get("/api/projekt/" + projekt.getId() + "/besitzer"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.NOT_FOUND.value()));
                    return null;
                });

        doWithApi(
                (api) -> api.patch("/api/projekt/" + projekt.getId(), RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, "application/merge-patch+json")
                        .setData(String.format("{\"besitzerId\":\"%s\"}",
                                nutzer1.getId()))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Projekt.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(nutzer1.getId(), json.getBesitzerId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.get("/api/projekt/" + projekt.getId() + "/besitzer"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Nutzer.parseJson(res.text());
                    assertEquals(nutzer1.getId(), json.getId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.patch("/api/projekt/" + projekt.getId(), RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, "application/merge-patch+json")
                        .setData(String.format("{\"besitzerId\":\"%s\"}",
                                nutzer2.getId()))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Projekt.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(nutzer2.getId(), json.getBesitzerId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.get("/api/projekt/" + projekt.getId() + "/besitzer"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Nutzer.parseJson(res.text());
                    assertEquals(nutzer2.getId(), json.getId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.patch("/api/projekt/" + projekt.getId(), RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, "application/merge-patch+json")
                        .setData("{\"besitzerId\":null}")),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Projekt.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertNull(json.getBesitzerId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.get("/api/projekt/" + projekt.getId() + "/besitzer"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.NOT_FOUND.value()));
                    return null;
                });
    }

    public void assertProjektMitglied() {
        final var allNutzerId = fetchAllNutzerId();
        assertTrue(allNutzerId.size() > 1);
        final var projekt = createRandomProjekt();

        doWithApi(
                (api) -> api.get("/api/projekt/" + projekt.getId() + "/allMitglied"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var jsonReader = new JsonMapper().parseJsonPath(res.text());
                    final var allMitgliedId = jsonReader.readContentId();
                    assertEquals(0, allMitgliedId.size());
                    return allMitgliedId;
                });

        doWithApi(
                (api) -> api.patch("/api/projekt/" + projekt.getId(), RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, "application/merge-patch+json")
                        .setData(String.format("{\"allMitglied\":[\"/api/nutzer/%s\",\"/api/nutzer/%s\"]}",
                                allNutzerId.get(0), allNutzerId.get(1)))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Projekt.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(0, json.getAllMitglied().size());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.get("/api/projekt/" + projekt.getId() + "/allMitglied"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var jsonReader = new JsonMapper().parseJsonPath(res.text());
                    final var allMitgliedId = jsonReader.readContentId();
                    assertEquals(2, allMitgliedId.size());
                    return allMitgliedId;
                });

        doWithApi(
                (api) -> api.patch("/api/projekt/" + projekt.getId(), RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, "application/merge-patch+json")
                        .setData(String.format("{\"allMitglied\":[\"/api/nutzer/%s\"]}",
                                allNutzerId.get(0)))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Projekt.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(0, json.getAllMitglied().size());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.get("/api/projekt/" + projekt.getId() + "/allMitglied"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var jsonReader = new JsonMapper().parseJsonPath(res.text());
                    final var allMitgliedId = jsonReader.readContentId();
                    assertEquals(1, allMitgliedId.size());
                    return allMitgliedId;
                });

        doWithApi(
                (api) -> api.patch("/api/projekt/" + projekt.getId(), RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, "application/merge-patch+json")
                        .setData("{\"allMitglied\":[]}")),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Projekt.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(0, json.getAllMitglied().size());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.get("/api/projekt/" + projekt.getId() + "/allMitglied"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var jsonReader = new JsonMapper().parseJsonPath(res.text());
                    final var allMitgliedId = jsonReader.readContentId();
                    assertEquals(0, allMitgliedId.size());
                    return allMitgliedId;
                });
    }

    public void assertProjektOption() {
        final var projekt = createRandomProjekt();
        assertTrue(projekt.getAllOption().isEmpty());

        doWithApi(
                (api) -> api.patch("/api/projekt/" + projekt.getId(), RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, "application/merge-patch+json")
                        .setData("{\"allOption\":[" +
                                "{\"typ\":\"A\",\"text\":\"Alpha\"}," +
                                "{\"typ\":\"B\",\"text\":\"Beta\"}" +
                                "]}")),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Projekt.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(2, json.getAllOption().size());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.patch("/api/projekt/" + projekt.getId(), RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, "application/merge-patch+json")
                        .setData("{\"allOption\":[" +
                                "{\"typ\":\"B\",\"text\":\"Beta\"}" +
                                "]}")),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Projekt.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(1, json.getAllOption().size());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.patch("/api/projekt/" + projekt.getId(), RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, "application/merge-patch+json")
                        .setData("{\"allOption\":[]}")),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Projekt.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(0, json.getAllOption().size());
                    return json.getId();
                });
    }

    public void assertProjektQuelle() {
        final var projekt = createRandomProjekt();
        assertTrue(projekt.getAllQuelle().isEmpty());

        doWithApi(
                (api) -> api.patch("/api/projekt/" + projekt.getId(), RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, "application/merge-patch+json")
                        .setData("{\"allQuelle\":[" +
                                "{\"typ\":\"A\",\"uri\":\"https://a.com\",\"text\":\"Alpha\"}," +
                                "{\"typ\":\"B\",\"uri\":\"https://b.com\",\"text\":\"Beta\"}" +
                                "]}")),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Projekt.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(2, json.getAllQuelle().size());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.patch("/api/projekt/" + projekt.getId(), RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, "application/merge-patch+json")
                        .setData("{\"allQuelle\":[" +
                                "{\"typ\":\"B\",\"uri\":\"https://b.com\",\"text\":\"Beta\"}" +
                                "]}")),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Projekt.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(1, json.getAllQuelle().size());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.patch("/api/projekt/" + projekt.getId(), RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, "application/merge-patch+json")
                        .setData("{\"allQuelle\":[]}")),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Projekt.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(0, json.getAllQuelle().size());
                    return json.getId();
                });
    }
}
