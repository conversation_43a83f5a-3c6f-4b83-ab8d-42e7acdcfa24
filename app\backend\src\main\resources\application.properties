# Kein Banner wegen JSON-Logging
spring.main.banner-mode=off
# org.springframework.boot.autoconfigure.jdbc.DatasourceProperties
spring.datasource.url=*****************************************
spring.datasource.username=sa
spring.datasource.password=P@ssw0rd
# org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties
spring.jpa.properties.hibernate.show_sql=false
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.properties.hibernate.generate_statistics=false
# org.springframework.boot.logging.LoggingSystemProperties
logging.level.web=INFO
logging.level.org.hibernate.SQL=INFO
logging.level.org.hibernate.orm.jdbc.bind=INFO
logging.level.org.hibernate.stat=INFO
