release={{.Release.Name}}
namespace={{.Release.Namespace}}
chart={{.Chart.Name}}
version={{.Chart.Version}}
repository={{.Values.image.repository}}
tag={{.Values.image.tag}}
database={{.Values.database.host}}:{{.Values.database.port}}
ingress={{.Values.ingress.class}}
clientImageName={{.Values.image.clientImageName}}
backendImageName={{.Values.image.backendImageName}}
printerImageName={{.Values.image.printerImageName}}
spoolerImageName={{.Values.image.spoolerImageName}}
adapterImageName={{.Values.image.adapterImageName}}
