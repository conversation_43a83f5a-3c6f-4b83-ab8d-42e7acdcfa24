<script>
  import ActionButton from "./ActionButton.svelte";

  // Example actions configuration
  const actions = [
    {
      id: "edit",
      label: "Edit",
      icon: "edit"
    },
    {
      id: "copy",
      label: "Copy",
      icon: "content_copy"
    },
    {
      id: "share",
      label: "Share",
      icon: "share"
    },
    {
      divider: true
    },
    {
      id: "archive",
      label: "Archive",
      icon: "archive"
    },
    {
      id: "delete",
      label: "Delete",
      icon: "delete",
      disabled: false
    }
  ];

  const adminActions = [
    {
      id: "settings",
      label: "Settings",
      icon: "settings"
    },
    {
      id: "users",
      label: "Manage Users",
      icon: "people"
    },
    {
      divider: true
    },
    {
      id: "backup",
      label: "Backup Data",
      icon: "backup"
    },
    {
      id: "logs",
      label: "View Logs",
      icon: "description"
    }
  ];

  function handleAction(event) {
    const { action, id } = event.detail;
    console.log("Action clicked:", action);
    alert(`Action "${action.label}" (${id}) was clicked!`);
  }
</script>

<div class="p-8 space-y-8">
  <h1 class="text-2xl font-bold text-gray-900">ActionButton Examples</h1>
  
  <div class="space-y-6">
    <!-- Primary variant -->
    <div class="space-y-2">
      <h2 class="text-lg font-semibold">Primary Button</h2>
      <ActionButton
        {actions}
        label="Actions"
        variant="primary"
        icon="more_vert"
        on:action={handleAction}
      />
    </div>

    <!-- Secondary variant -->
    <div class="space-y-2">
      <h2 class="text-lg font-semibold">Secondary Button</h2>
      <ActionButton
        {actions}
        label="Options"
        variant="secondary"
        icon="settings"
        on:action={handleAction}
      />
    </div>

    <!-- Outline variant -->
    <div class="space-y-2">
      <h2 class="text-lg font-semibold">Outline Button</h2>
      <ActionButton
        {actions}
        label="Menu"
        variant="outline"
        icon="menu"
        on:action={handleAction}
      />
    </div>

    <!-- Ghost variant -->
    <div class="space-y-2">
      <h2 class="text-lg font-semibold">Ghost Button</h2>
      <ActionButton
        {actions}
        label="More"
        variant="ghost"
        icon="more_horiz"
        on:action={handleAction}
      />
    </div>

    <!-- Different sizes -->
    <div class="space-y-2">
      <h2 class="text-lg font-semibold">Different Sizes</h2>
      <div class="flex items-center space-x-4">
        <ActionButton
          {actions}
          label="Small"
          size="sm"
          variant="primary"
          on:action={handleAction}
        />
        <ActionButton
          {actions}
          label="Medium"
          size="md"
          variant="primary"
          on:action={handleAction}
        />
        <ActionButton
          {actions}
          label="Large"
          size="lg"
          variant="primary"
          on:action={handleAction}
        />
      </div>
    </div>

    <!-- Admin actions example -->
    <div class="space-y-2">
      <h2 class="text-lg font-semibold">Admin Actions</h2>
      <ActionButton
        actions={adminActions}
        label="Admin"
        variant="secondary"
        icon="admin_panel_settings"
        on:action={handleAction}
      />
    </div>

    <!-- Disabled state -->
    <div class="space-y-2">
      <h2 class="text-lg font-semibold">Disabled Button</h2>
      <ActionButton
        {actions}
        label="Disabled"
        variant="primary"
        disabled={true}
        on:action={handleAction}
      />
    </div>

    <!-- Without icon -->
    <div class="space-y-2">
      <h2 class="text-lg font-semibold">Without Icon</h2>
      <ActionButton
        {actions}
        label="Text Only"
        variant="outline"
        icon={null}
        on:action={handleAction}
      />
    </div>
  </div>
</div>
