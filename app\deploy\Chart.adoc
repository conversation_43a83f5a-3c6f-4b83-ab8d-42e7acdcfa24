= Spezifikation für das Deployment mit einer PostgrSQL-Datenbank

https://www.postgresql.org/

https://bitnami.com/stack/postgresql/containers

.postgres.yaml
[source,yaml,options="nowrap"]
----
include::templates/postgres.yaml[]
----

.ingress.yaml
[source,yaml,options="nowrap"]
----
include::templates/ingress.yaml[]
----

tbd Ingress-Nginx

tbd `/api`

tbd `/doc`

tbd `/login`

tbd `/mail`

tbd `/oauth2`

tbd `/print`

tbd `/version`

tbd `/health` nur intern

tbd `/jwt` nur intern

tbd `/who` nur intern

.server.yaml
[source,yaml,options="nowrap"]
----
include::templates/server.yaml[]
----

tbd Deployment

tbd Startup-Probe

tbd Liveness-Probe

tbd Readiness-Probe

.client.yaml
[source,yaml,options="nowrap"]
----
include::templates/client.yaml[]
----

tbd Deployment

tbd Startup-Probe

tbd Liveness-Probe

tbd Readiness-Probe

.adapter.yaml
[source,yaml,options="nowrap"]
----
include::templates/adapter.yaml[]
----

tbd Beispiel für das Anlegen von statischen Daten

tbd Beispiel für die Anbindung einer externen Datenquelle

tbd Deployment

.values.yaml
[source,yaml,options="nowrap"]
----
include::values.yaml[]
----

tbd traefik oder ingreas-nginx
