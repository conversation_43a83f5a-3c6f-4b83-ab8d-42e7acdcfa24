<script>
  import { createEventDispatcher } from "svelte";
  import { toast } from "../components/Toast";
  import { updatePatch } from "../utils/rest.js";
  import { removeValue } from "../utils/rest.js";
  import Icon from "../components/Icon";
  import LinkRef from "../components/LinkRef";
  import Select from "../components/Select";
  import TextField from "../components/TextField";
  import TextArea from "../components/TextArea";

  export let aufgabe;
  export let rows = 4;
  export let allNutzerItem;

  let clicked = false;
  async function onChange() {
    try {
      clicked = true;
      if (!aufgabe.nutzerId) {
        aufgabe.nutzerId = null;
      }
      aufgabe.status = aufgabe.aktiv ? "I" : "X";
      await updateAufgabe();
    } finally {
      clicked = false;
    }
  }
  async function onRemove() {
    try {
      clicked = true;
      await removeAufgabe();
    } finally {
      clicked = false;
    }
  }

  const dispatch = createEventDispatcher();
  function updateAufgabe() {
    clicked = true;
    return updatePatch("/api/aufgabe" + "/" + aufgabe.id, aufgabe)
      .then((json) => {
        console.log(["updateAufgabe", aufgabe, json]);
        aufgabe = { ...aufgabe, ...json };
        dispatch("update", json);
      })
      .catch((err) => {
        console.log(["updateAufgabe", aufgabe, err]);
        toast.push(err.toString());
      });
  }
  function removeAufgabe() {
    const text = aufgabe.titel;
    const hint = text.length > 20 ? text.substring(0, 20) + "..." : text;
    if (!confirm("Aufgabe '" + hint + "' wirklich löschen?")) return;
    return removeValue("/api/aufgabe" + "/" + aufgabe.id)
      .then((json) => {
        console.log(["removeAufgabe", aufgabe, json]);
        dispatch("remove", json);
      })
      .catch((err) => {
        console.log(["removeAufgabe", aufgabe, err]);
        toast.push(err.toString());
      });
  }
</script>

<div class="block p-2 rounded-lg shadow-lg">
  <div class="flex flex-col gap-1 pt-2">
    <div class="flex flex-row gap-1 items-baseline">
      <div class="w-full">
        <Select
          bind:value={aufgabe.nutzerId}
          on:change={onChange}
          allItem={allNutzerItem}
          valueGetter={(v) => v?.value}
          disabled={clicked}
          nullable
          label="Nutzer"
          placeholder="Bitte einen Nutzer wählen"
        />
      </div>
      <div class="w-min">
        <Icon
          bind:checked={aufgabe.aktiv}
          on:click={onChange}
          disabled={clicked}
          name={aufgabe.aktiv ? "check" : "redo"}
          outlined
        />
      </div>
      <div class="w-min">
        <Icon on:click={onRemove} disabled={clicked} name="delete" outlined />
      </div>
    </div>
    <div class="w-full">
      <TextField
        bind:value={aufgabe.titel}
        on:change={onChange}
        required
        disabled={clicked}
        label="Titel"
        placeholder="Bitte einen Titel eingeben"
      />
    </div>
    <div class="w-full">
      <TextArea
        bind:value={aufgabe.text}
        on:change={onChange}
        required
        disabled={clicked}
        label="Text"
        title="Bitte einen Text eingeben."
        {rows}
      />
    </div>
    <div class="w-full">
      {#if aufgabe.quelle}
        <LinkRef path={aufgabe.quelle.uri} bind:value={aufgabe.quelle.uri} />
      {:else}
        <LinkRef value="Kein Quelle" disabled />
      {/if}
    </div>
    <div class="w-full">
      <div class="flex flex-row flex-wrap gap-1">
        {#each aufgabe.allStichwort as text}
          <span class="p-1 text-xs text-white bg-primary-500 rounded">
            {text}
          </span>
        {/each}
      </div>
    </div>
  </div>

  {#if import.meta.env.DEV}
    <details tabindex="-1">
      <summary tabindex="-1">JSON</summary>
      <pre>{JSON.stringify(aufgabe, null, 2)}</pre>
    </details>
  {/if}
</div>
