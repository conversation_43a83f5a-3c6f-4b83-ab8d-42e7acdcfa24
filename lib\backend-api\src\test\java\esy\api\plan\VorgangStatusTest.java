package esy.api.plan;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import static esy.api.plan.VorgangStatus.*;
import static org.junit.jupiter.api.Assertions.assertEquals;

class VorgangStatusTest {

    @Test
    void ordinal() {
        assertEquals(0, I.ordinal());
        assertEquals(1, B.ordinal());
        assertEquals(2, A.ordinal());
        assertEquals(3, X.ordinal());
    }

    @Test
    void ofI() {
        assertEquals(I, VorgangStatus.fromText("identifiziert").orElseThrow());
        assertEquals(I, VorgangStatus.fromString("i"));
        assertEquals(I, VorgangStatus.fromString("I"));
    }

    @Test
    void ofB() {
        assertEquals(B, VorgangStatus.fromText("bestätigt").orElseThrow());
        assertEquals(B, VorgangStatus.fromString("b"));
        assertEquals(B, VorgangStatus.fromString("B"));
    }

    @Test
    void ofA() {
        assertEquals(A, VorgangStatus.fromText("abgelehnt").orElseThrow());
        assertEquals(A, VorgangStatus.fromString("a"));
        assertEquals(A, VorgangStatus.fromString("A"));
    }

    @Test
    void ofX() {
        assertEquals(X, VorgangStatus.fromText("geschlossen").orElseThrow());
        assertEquals(X, VorgangStatus.fromString("x"));
        assertEquals(X, VorgangStatus.fromString("X"));
    }

    @Test
    void forCreate() {
        assertEquals(I, VorgangStatus.forCreate());
    }

    @ParameterizedTest
    @CsvSource({
            "I,I,true",
            "I,A,true",
            "I,B,true",
            "I,X,true",
            "A,I,false",
            "A,A,true",
            "A,B,true",
            "A,X,true",
            "B,I,false",
            "B,A,true",
            "B,B,true",
            "B,X,true",
            "X,I,true",
            "X,A,false",
            "X,B,false",
            "X,X,true",
    })
    void forUpdate(final String textOld, final String textNew, boolean ok) {
        final var statusOld = VorgangStatus.fromString(textOld);
        final var statusNew = VorgangStatus.fromString(textNew);
        assertEquals(ok, VorgangStatus.forUpdate(statusOld).contains(statusNew));
    }

    @Test
    void forDelete() {
        assertEquals(X, VorgangStatus.forDelete());
    }
}
