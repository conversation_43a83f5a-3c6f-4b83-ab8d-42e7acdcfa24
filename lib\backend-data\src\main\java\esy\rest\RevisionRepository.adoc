:projectDir: ../../../../../../..
:term: RevisionRepository
= Spezifikation für eine {term}-<PERSON><PERSON><PERSON>

Ein<PERSON> {term}-Klasse realisiert Logik für die REST-Schnittstelle.
Hibernate Envers hilft dabei, Datenbankoperationen, welche Änderungen verursachen, zu protokollieren.
Jede Änderung ist eine Revision.

TIP: Siehe
https://docs.spring.io/spring-data/jpa/reference/envers.html[Spring Data JPA Reference Guide]

== Konfiguration

[source,java,options="nowrap"]
----
@EnableEnversRepositories(basePackages = {..})
----

tbd RevisionRepository

== Eigenschaften

Keine

== Operationen

tbd