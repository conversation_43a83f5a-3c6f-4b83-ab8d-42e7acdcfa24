package esy.app.info;

import com.microsoft.playwright.Playwright;
import com.microsoft.playwright.options.RequestOptions;
import esy.api.info.Enum;
import esy.app.PlaywrightApiAssertionBase;
import esy.app.PlaywrightApiTestEnv;
import esy.json.JsonMapper;
import lombok.NonNull;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.jupiter.api.Assertions.*;

@SuppressWarnings("java:S1192") // duplicating literals is ok
public class EnumPlaywrightApiAssertion extends PlaywrightApiAssertionBase {

    public EnumPlaywrightApiAssertion(@NonNull final Playwright playwright, @NonNull final PlaywrightApiTestEnv env) {
        super(playwright, env);
        login();
    }

    public void assertEnumHistory() {
        final var art = randomName();
        final var code = 1;
        final var name = "Ratte";
        final var enumId = doWithApi(
                (api) -> api.post(String.format("/api/enum/%s", art), RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"code\":\"%d\",\"name\":\"%s\",\"text\":\"%s\"}",
                                code, name.toUpperCase(), name))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.CREATED.value()));
                    final var json = Enum.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(code, json.getCode());
                    assertEquals(name.toUpperCase(), json.getName());
                    assertEquals(name, json.getText());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.put(String.format("/api/enum/%s/%d", art, code), RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"code\":\"%d\",\"name\":\"%s\",\"text\":\"%s\"}",
                                code, name, name))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Enum.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(code, json.getCode());
                    assertEquals(name, json.getName());
                    assertEquals(name, json.getText());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.delete(String.format("/api/enum/%s/%d", art, code)),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Enum.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(code, json.getCode());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.get("/api/enum/" + enumId + "/history"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var jsonReader = new JsonMapper().parseJsonPath(res.text());
                    final var allName = jsonReader.readContentField("name");
                    assertEquals(3, allName.size());
                    assertEquals(name.toUpperCase(), allName.get(0));
                    assertEquals(name, allName.get(1));
                    assertEquals(name, allName.get(2));
                    return null;
                });
    }

    public void assertEnumKanal() {
        final var allKanal = doWithApi(
                (api) -> api.get("/api/enum/kanal"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    return new JsonMapper().parseJsonContent(res.text(), Enum.class);
                });
        assertTrue(allKanal.size() > 1);
        final var allKanalName = allKanal.stream().map(Enum::getName).toList();
        assertTrue(allKanalName.contains("tel"));
        assertTrue(allKanalName.contains("mail"));
    }

    public void assertEnumQuelle() {
        final var allQuelle = doWithApi(
                (api) -> api.get("/api/enum/quelle"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    return new JsonMapper().parseJsonContent(res.text(), Enum.class);
                });
        assertTrue(allQuelle.size() > 1);
        final var allQuelleName = allQuelle.stream().map(Enum::getName).toList();
        assertTrue(allQuelleName.contains("JIRA"));
        assertTrue(allQuelleName.contains("TODO"));
    }

    public void assertEnumSprache() {
        final var allSprache = doWithApi(
                (api) -> api.get("/api/enum/sprache"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    return new JsonMapper().parseJsonContent(res.text(), Enum.class);
                });
        assertTrue(allSprache.size() > 2);
        final var allSpracheName = allSprache.stream().map(Enum::getName).toList();
        assertTrue(allSpracheName.contains("DE"));
        assertTrue(allSpracheName.contains("EN"));
        assertTrue(allSpracheName.contains("FI"));
    }
}
