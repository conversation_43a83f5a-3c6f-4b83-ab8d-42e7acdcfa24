plugins {
    id 'base'
}

apply from: "${rootDir}/gradle/kube-build.gradle"
apply from: "${rootDir}/gradle/helm-build.gradle"

tasks.register('dockerBuildImage') {
    group = 'deployment'
    mustRunAfter 'build'
    dependsOn ':app:client-svelte:buildImage'
    dependsOn ':app:backend:buildImage'
    dependsOn ':app:printer:buildImage'
    dependsOn ':app:spooler:buildImage'
    dependsOn ':app:adapter:buildImage'
}

tasks.register('dockerSaveImage') {
    group = 'deployment'
    mustRunAfter 'dockerBuildImage'
    doFirst {
        mkdir project.buildDir
    }
    doLast {
        k3sSaveImage('client-svelte', project.buildDir);
        k3sSaveImage('backend', project.buildDir);
        k3sSaveImage('printer', project.buildDir);
        k3sSaveImage('spooler', project.buildDir);
        k3sSaveImage('adapter', project.buildDir);
    }
}

tasks.register('keycloakUp', Exec) {
    group = 'deployment'
    commandLine 'docker', 'compose', '-p', rootProject.name, '-f', 'compose-keycloak.yml', 'up',  \
         '--detach',  \
         '--no-build'
}

tasks.register('postgresUp', Exec) {
    group = 'deployment'
    commandLine 'docker', 'compose', '-p', rootProject.name, '-f', 'compose.yml', 'up', 'postgres17',  \
         '--detach',  \
         '--no-build'
}

tasks.register('composeUp', Exec) {
    group = 'deployment'
    mustRunAfter 'dockerBuildImage'
    commandLine 'docker', 'compose', '-p', rootProject.name, '-f', 'compose.yml', 'up',  \
         '--detach',  \
         '--no-build'
}

tasks.register('composeStop', Exec) {
    group = 'deployment'
    commandLine 'docker', 'compose', '-p', rootProject.name, '-f', 'compose.yml', 'stop'
}

tasks.register('composeDown', Exec) {
    group = 'deployment'
    commandLine 'docker', 'compose', '-p', rootProject.name, '-f', 'compose.yml', 'down'
}

tasks.register('installRancherK3s') {
    group = 'deployment'
    mustRunAfter 'dockerSaveImage'
    doLast {
        kubectlGet('pods');
        helmInstallProject([
                'application.login': "keycloak",
                'keycloak.baseUri' : "https://keycloak.cardsplus.info",
                // database installed with helm
                'database.host'    : "postgres17.postgres.svc.cluster.local",
                'database.port'    : 5432,
                'database.schema'  : rootProject.name
        ]);
        kubectlGet('pods');
    }
}

tasks.register('installRancherDesktop') {
    group = 'deployment'
    mustRunAfter 'dockerBuildImage'
    dependsOn 'postgresUp'
    doLast {
        kubectlGet('pods');
        helmInstallProject([
                'application.login': "github",
                // database created with docker compose
                'database.host'    : LOCALHOST,
                'database.port'    : 5432,
                'database.schema'  : rootProject.name
        ]);
        kubectlGet('pods');
    }
}

tasks.register('installDockerDesktop') {
    group = 'deployment'
    mustRunAfter 'dockerBuildImage'
    dependsOn 'postgresUp'
    doLast {
        kubectlGet('pods');
        helmInstallProject([
                'application.login': "github",
                // database created with docker compose
                'database.host'    : LOCALHOST,
                'database.port'    : 5432,
                'database.schema'  : rootProject.name
        ]);
        kubectlGet('pods');
    }
}

tasks.register('uninstall') {
    group = 'deployment'
    doLast {
        kubectlGet('pods');
        helmUninstallProject();
        kubectlGet('pods');
    }
}

tasks.register('describe') {
    group = 'deployment'
    doLast {
        kubectlDescribePod('ingress');
        kubectlDescribePod('client')
        kubectlDescribePod('server')
    }
}

tasks.register('installNginx') {
    group = 'deployment'
    doLast {
        kubectlGet('pods');
        helmInstallNginx([:]);
        kubectlGet('pods');
    }
}

tasks.register('uninstallNginx') {
    group = 'deployment'
    doLast {
        kubectlGet('pods');
        helmUninstallNginx();
        kubectlGet('pods');
    }
}

tasks.register('installTraefik') {
    group = 'deployment'
    doLast {
        kubectlGet('pods');
        helmInstallTraefik([:]);
        kubectlGet('pods');
    }
}

tasks.register('uninstallTraefik') {
    group = 'deployment'
    doLast {
        kubectlGet('pods');
        helmUninstallTraefik();
        kubectlGet('pods');
    }
}
