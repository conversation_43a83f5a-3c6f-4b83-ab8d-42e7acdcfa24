<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.9.xsd">
    <changeSet id="1" author="robert">
        <preConditions onFail="MARK_RAN">
            <not><tableExists tableName="aufgabe_aud" /></not>
        </preConditions>
        <createTable tableName="aufgabe_aud">
            <column name="rev" type="BIGINT">
                <constraints foreignKeyName="fk_aufgabe_aud_revinfo"
                             referencedTableName="revinfo"
                             referencedColumnNames="id"
                             nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
            <column name="id" type="UUID">
                <constraints nullable="true"/>
            </column>
            <column name="projekt_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="nutzer_id" type="UUID">
                <constraints nullable="true"/>
            </column>
            <column name="titel" type="VARCHAR(512)">
                <constraints nullable="true"/>
            </column>
            <column name="text" type="VARCHAR(8196)">
                <constraints nullable="true"/>
            </column>
            <column name="termin" type="DATE">
                <constraints nullable="true"/>
            </column>
            <column name="terminserie" type="VARCHAR(16)">
                <constraints nullable="true"/>
            </column>
            <column name="status" type="VARCHAR(64)">
                <constraints nullable="true"/>
            </column>
            <column name="quelle_typ" type="VARCHAR(512)">
                <constraints nullable="true"/>
            </column>
            <column name="quelle_uri" type="VARCHAR(512)">
                <constraints nullable="true"/>
            </column>
            <column name="quelle_text" type="VARCHAR(512)">
                <constraints nullable="true"/>
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>
