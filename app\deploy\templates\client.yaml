apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: {{.Release.Namespace}}
  name: client-deployment
spec:
  replicas: 1
  selector:
    matchLabels:
      component: client
  template:
    metadata:
      labels:
        release: {{.Release.Name}}
        version: {{.Chart.Version}}
        chart: {{.Chart.Name}}
        component: client
    spec:
{{ if .Values.image.secret }}
      imagePullSecrets:
        - name: {{ .Values.image.secret }}
{{ end }}
      containers:
        - name: client-container
          image: {{.Values.image.repository}}/{{.Values.image.clientImageName}}:{{.Values.image.tag}}
          imagePullPolicy: IfNotPresent
          # tag::env[]
          # No values
          # end::env[]
          # tag::ports[]
          ports:
            - containerPort: {{.Values.image.clientPort}}
              name: client-http
          # end::ports[]
          # tag::resources[]
          resources:
            requests:
              cpu: {{.Values.image.clientCpu}}
              memory: {{.Values.image.clientMemory}}
            limits:
              #cpu: no limit on single node
              memory: {{.Values.image.clientMemory}}
          # tag::resources[]
          # tag::readiness[]
          readinessProbe:
            initialDelaySeconds: 5
            periodSeconds: 3
            failureThreshold: 10
            timeoutSeconds: 1
            httpGet:
              port: client-http
              path: /home
          # end::readiness[]
          # tag::liveness[]
          # No values
          # end::liveness[]
---
apiVersion: v1
kind: Service
metadata:
  namespace: {{.Release.Namespace}}
  name: client-cluster-ip
spec:
  type: ClusterIP
  selector:
    component: client
  ports:
    - port: {{.Values.image.clientPort}}
      name: client-http
      targetPort: client-http
