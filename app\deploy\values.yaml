application:  
  # Der Wert wird durch --set überschrieben.
  name: undefined
  login: ""

database:
  name: postgres
  # Der Wert wird durch --set überschrieben.
  host: undefined
  port: 5432
  schema: public
  
keycloak:
  # Der Wert wird durch --set überschrieben.
  baseUri: undefined
  realm: master

# https://github.com/kubernetes/ingress-nginx/
# https://github.com/traefik/traefik/
ingress:
  class: traefik
  host: ""

image:
  # Die URL für den Abruf der Docker-Images.
  # Der Wert wird durch --set überschrieben.
  repository: undefined
  # Das Tag für den Abruf der Docker-Images.
  # Der Wert wird durch --set überschrieben.
  tag: latest
  # Der Name des Docker-Images für den Client.
  clientImageName: client-svelte
  clientPort: 5000
  clientCpu: "500m"
  clientMemory: "256Mi"
  # Der Name des Docker-Images für den Server.
  backendImageName: backend
  backendPort: 8080
  backendCpu: "500m"
  backendMemory: "1Gi"
  # Der Name des Docker-Images für den Printer.
  printerImageName: printer
  printerPort: 8081
  printerCpu: "500m"
  printerMemory: "1Gi"
  # Der Name des Docker-Images für den Spooler.
  spoolerImageName: spooler
  spoolerPort: 8082
  spoolerCpu: "500m"
  spoolerMemory: "1Gi"
  # Der Name des Docker-Images für den Adapter.
  adapterImageName: adapter
  adapterCpu: "100m"
  adapterMemory: "256Mi"
  