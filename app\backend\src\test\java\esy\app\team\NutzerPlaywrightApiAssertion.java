package esy.app.team;

import com.microsoft.playwright.Playwright;
import com.microsoft.playwright.options.RequestOptions;
import esy.api.info.Sprache;
import esy.api.team.Nutzer;
import esy.app.PlaywrightApiAssertionBase;
import esy.app.PlaywrightApiTestEnv;
import esy.json.JsonMapper;
import lombok.NonNull;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;

import java.util.UUID;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.jupiter.api.Assertions.*;

@SuppressWarnings("java:S1192") // duplicating literals is ok
public class NutzerPlaywrightApiAssertion extends PlaywrightApiAssertionBase {

    public NutzerPlaywrightApiAssertion(@NonNull final Playwright playwright, @NonNull final PlaywrightApiTestEnv env) {
        super(playwright, env);
        login();
    }

    public void assertNutzer() {
        final var nutzerMail1 = "<EMAIL>";
        final var nutzerName1 = "Alf Mustermann";
        final var nutzerId1 = doWithApi(
                (api) -> api.post("/api/nutzer", RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"mail\":\"%s\",\"name\":\"%s\",\"aktiv\":\"false\"}",
                                nutzerMail1, nutzerName1))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.CREATED.value()));
                    final var json = Nutzer.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(nutzerMail1, json.getMail());
                    assertEquals(nutzerName1, json.getName());
                    assertFalse(json.isAktiv());
                    assertEquals(0, json.getAllSprache().size());
                    assertEquals(0, json.getAllGruppe().size());
                    assertEquals("", json.getProfil().getName());
                    assertFalse(json.getProfil().isChat());
                    assertTrue(json.getProfil().isMail());
                    assertFalse(json.getProfil().isWiki());
                    return json.getId();
                });

        final var nutzerMail2 = "<EMAIL>";
        final var nutzerName2 = "Bea Musterfrau";
        final var nutzerId2 = doWithApi(
                (api) -> api.put("/api/nutzer/" + UUID.randomUUID(), RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"mail\":\"%s\",\"name\":\"%s\",\"aktiv\":\"false\"}",
                                nutzerMail2, nutzerName2))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.CREATED.value()));
                    final var json = Nutzer.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(nutzerMail2, json.getMail());
                    assertEquals(nutzerName2, json.getName());
                    assertFalse(json.isAktiv());
                    assertEquals(0, json.getAllSprache().size());
                    assertEquals(0, json.getAllGruppe().size());
                    assertEquals("", json.getProfil().getName());
                    assertFalse(json.getProfil().isChat());
                    assertTrue(json.getProfil().isMail());
                    assertFalse(json.getProfil().isWiki());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.put("/api/nutzer/" + nutzerId2, RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"mail\":\"%s\",\"name\":\"%s\",\"aktiv\":\"true\"}",
                                nutzerMail2, nutzerName2))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Nutzer.parseJson(res.text());
                    assertEquals(nutzerId2, json.getId());
                    assertTrue(json.isAktiv());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.patch("/api/nutzer/" + nutzerId2, RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, "application/merge-patch+json")
                        .setData("{\"profil\":{\"name\":\"Bea\"}}")),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Nutzer.parseJson(res.text());
                    assertEquals(nutzerId2, json.getId());
                    assertEquals("Bea", json.getProfil().getName());
                    assertFalse(json.getProfil().isChat());
                    assertTrue(json.getProfil().isMail());
                    assertFalse(json.getProfil().isWiki());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.get("/api/nutzer"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var jsonReader = new JsonMapper().parseJsonPath(res.text());
                    assertNotNull(jsonReader.read("$.page"));
                    final var allNutzerMail = jsonReader.readContentField("mail");
                    assertTrue(allNutzerMail.contains(nutzerMail1));
                    assertTrue(allNutzerMail.contains(nutzerMail2));
                    final var allNutzerId = jsonReader.readContentId();
                    assertTrue(allNutzerId.contains(nutzerId1.toString()));
                    assertTrue(allNutzerId.contains(nutzerId2.toString()));
                    return allNutzerId;
                });

        doWithApi(
                (api) -> api.get("/api/nutzer/" + nutzerId1),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Nutzer.parseJson(res.text());
                    assertEquals(nutzerId1, json.getId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.get("/api/nutzer/search/findByMail", RequestOptions.create()
                        .setQueryParam("mail", nutzerMail1)),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Nutzer.parseJson(res.text());
                    assertEquals(nutzerId1, json.getId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.delete("/api/nutzer/" + nutzerId1),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Nutzer.parseJson(res.text());
                    assertEquals(nutzerId1, json.getId());
                    assertEquals(nutzerMail1, json.getMail());
                    assertEquals(nutzerName1, json.getName());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.delete("/api/nutzer/" + nutzerId2),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Nutzer.parseJson(res.text());
                    assertEquals(nutzerId2, json.getId());
                    assertEquals(nutzerMail2, json.getMail());
                    assertEquals(nutzerName2, json.getName());
                    return json.getId();
                });
    }

    public void assertNutzerSprache() {
        final var nutzer = createRandomNutzer();
        assertTrue(nutzer.getAllSprache().isEmpty());

        doWithApi(
                (api) -> api.patch("/api/nutzer/" + nutzer.getId(), RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, "application/merge-patch+json")
                        .setData("{\"allSprache\":[\"DE\",\"EN\",\"IT\"]}")),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Nutzer.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(3, json.getAllSprache().size());
                    assertTrue(json.getAllSprache().contains(Sprache.DE.name()));
                    assertTrue(json.getAllSprache().contains(Sprache.EN.name()));
                    assertTrue(json.getAllSprache().contains(Sprache.IT.name()));
                    return json.getId();
                });

        doWithApi(
                (api) -> api.patch("/api/nutzer/" + nutzer.getId(), RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, "application/merge-patch+json")
                        .setData("{\"allSprache\":[\"XX\"]}")),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Nutzer.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(1, json.getAllSprache().size());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.patch("/api/nutzer/" + nutzer.getId(), RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, "application/merge-patch+json")
                        .setData("{\"allSprache\":[]}")),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Nutzer.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(0, json.getAllSprache().size());
                    return json.getId();
                });
    }

    public void assertNutzerKanal() {
        final var nutzer = createRandomNutzer();
        assertTrue(nutzer.getAllKanal().isEmpty());

        doWithApi(
                (api) -> api.patch("/api/nutzer/" + nutzer.getId(), RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, "application/merge-patch+json")
                        .setData("{\"allKanal\":[" +
                                "{\"typ\":\"A\",\"wert\":\"a\",\"text\":\"Alpha\"}," +
                                "{\"typ\":\"B\",\"wert\":\"b\",\"text\":\"Beta\"}" +
                                "]}")),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Nutzer.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(2, json.getAllKanal().size());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.patch("/api/nutzer/" + nutzer.getId(), RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, "application/merge-patch+json")
                        .setData("{\"allKanal\":[" +
                                "{\"typ\":\"B\",\"wert\":\"b\",\"text\":\"Beta\"}" +
                                "]}")),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Nutzer.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(1, json.getAllKanal().size());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.patch("/api/nutzer/" + nutzer.getId(), RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, "application/merge-patch+json")
                        .setData("{\"allKanal\":[]}")),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Nutzer.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(0, json.getAllKanal().size());
                    return json.getId();
                });
    }

    public void assertNutzerGruppe() {
        final var allGruppeId = fetchAllGruppeId();
        assertTrue(allGruppeId.size() > 1);
        final var nutzer = createRandomNutzer();

        doWithApi(
                (api) -> api.get("/api/nutzer/" + nutzer.getId() + "/allGruppe"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var jsonReader = new JsonMapper().parseJsonPath(res.text());
                    final var allNutzerGruppeId = jsonReader.readContentId();
                    assertEquals(0, allNutzerGruppeId.size());
                    return allNutzerGruppeId;
                });

        doWithApi(
                (api) -> api.patch("/api/nutzer/" + nutzer.getId(), RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, "application/merge-patch+json")
                        .setData(String.format("{\"allGruppe\":[\"/api/gruppe/%s\",\"/api/gruppe/%s\"]}",
                                allGruppeId.get(0), allGruppeId.get(1)))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Nutzer.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(0, json.getAllGruppe().size());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.get("/api/nutzer/" + nutzer.getId() + "/allGruppe"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var jsonReader = new JsonMapper().parseJsonPath(res.text());
                    final var allNutzerGruppeId = jsonReader.readContentId();
                    assertEquals(2, allNutzerGruppeId.size());
                    return allNutzerGruppeId;
                });

        doWithApi(
                (api) -> api.patch("/api/nutzer/" + nutzer.getId(), RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, "application/merge-patch+json")
                        .setData(String.format("{\"allGruppe\":[\"/api/gruppe/%s\"]}",
                                allGruppeId.get(0)))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Nutzer.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(0, json.getAllGruppe().size());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.get("/api/nutzer/" + nutzer.getId() + "/allGruppe"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var jsonReader = new JsonMapper().parseJsonPath(res.text());
                    final var allNutzerGruppeId = jsonReader.readContentId();
                    assertEquals(1, allNutzerGruppeId.size());
                    return allNutzerGruppeId;
                });

        doWithApi(
                (api) -> api.patch("/api/nutzer/" + nutzer.getId(), RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, "application/merge-patch+json")
                        .setData("{\"allGruppe\":[]}")),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Nutzer.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(0, json.getAllGruppe().size());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.get("/api/nutzer/" + nutzer.getId() + "/allGruppe"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var jsonReader = new JsonMapper().parseJsonPath(res.text());
                    final var allNutzerGruppeId = jsonReader.readContentId();
                    assertEquals(0, allNutzerGruppeId.size());
                    return allNutzerGruppeId;
                });
    }
}
