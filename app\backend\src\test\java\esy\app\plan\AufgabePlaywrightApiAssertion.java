package esy.app.plan;

import com.microsoft.playwright.Playwright;
import com.microsoft.playwright.options.RequestOptions;
import esy.api.plan.Aufgabe;
import esy.api.plan.Risiko;
import esy.api.team.Nutzer;
import esy.app.PlaywrightApiAssertionBase;
import esy.app.PlaywrightApiTestEnv;
import esy.json.JsonMapper;
import lombok.NonNull;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;

import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.jupiter.api.Assertions.*;

@SuppressWarnings("java:S1192") // duplicating literals is ok
public class AufgabePlaywrightApiAssertion extends PlaywrightApiAssertionBase {

    public AufgabePlaywrightApiAssertion(@NonNull final Playwright playwright, @NonNull final PlaywrightApiTestEnv env) {
        super(playwright, env);
        login();
    }

    public void assertAufgabe() {
        final var projektId1 = createRandomProjekt().getId();
        final var projektId2 = createRandomProjekt().getId();

        doWithApi(
                (api) -> api.get("/api/projekt/" + projektId1 + "/allAufgabe"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var jsonReader = new JsonMapper().parseJsonPath(res.text());
                    final var allAufgabeId = jsonReader.readContentId();
                    assertEquals(0, allAufgabeId.size());
                    return allAufgabeId;
                });

        final var aufgabeTitel1 = "Aufgabe A";
        final var aufgabeId1 = doWithApi(
                (api) -> api.post("/api/aufgabe", RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"titel\":\"%s\",\"projektId\":\"%s\"}",
                                aufgabeTitel1, projektId1))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.CREATED.value()));
                    final var json = Aufgabe.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(aufgabeTitel1, json.getTitel());
                    assertNotNull(json.getProjektId());
                    return json.getId();
                });

        final var aufgabeTitel2 = "Aufgabe B";
        final var aufgabeId2 = doWithApi(
                (api) -> api.post("/api/aufgabe", RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"titel\":\"%s\",\"projektId\":\"%s\"}",
                                aufgabeTitel2, projektId1))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.CREATED.value()));
                    final var json = Aufgabe.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(aufgabeTitel2, json.getTitel());
                    assertNotNull(json.getProjektId());
                    return json.getId();
                });

        List.of("X", "I", "B", "B", "A", "A").forEach(status -> {
            doWithApi(
                    (api) -> api.patch("/api/aufgabe/" + aufgabeId2, RequestOptions.create()
                            .setHeader(HttpHeaders.CONTENT_TYPE, "application/merge-patch+json")
                            .setData("{\"status\":\"" + status + "\"}")),
                    (res) -> {
                        assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                        final var json = Risiko.parseJson(res.text());
                        assertNotNull(json.getId());
                        assertEquals(status, json.getStatus().name());
                        return json.getId();
                    });
        });

        doWithApi(
                (api) -> api.get("/api/projekt/" + projektId1 + "/allAufgabe"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var jsonReader = new JsonMapper().parseJsonPath(res.text());
                    final var allAufgabeId = jsonReader.readContentId();
                    assertEquals(2, allAufgabeId.size());
                    assertTrue(allAufgabeId.contains(aufgabeId1.toString()));
                    assertTrue(allAufgabeId.contains(aufgabeId2.toString()));
                    return allAufgabeId;
                });

        doWithApi(
                (api) -> api.patch("/api/aufgabe/" + aufgabeId2, RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"projektId\":\"%s\"}",
                                projektId2))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Aufgabe.parseJson(res.text());
                    assertEquals(aufgabeId2, json.getId());
                    assertEquals(projektId2, json.getProjektId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.get("/api/projekt/" + projektId1 + "/allAufgabe"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var jsonReader = new JsonMapper().parseJsonPath(res.text());
                    final var allAufgabeId = jsonReader.readContentId();
                    assertEquals(1, allAufgabeId.size());
                    assertTrue(allAufgabeId.contains(aufgabeId1.toString()));
                    return allAufgabeId;
                });

        doWithApi(
                (api) -> api.delete("/api/aufgabe/" + aufgabeId1),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Aufgabe.parseJson(res.text());
                    assertEquals(aufgabeId1, json.getId());
                    assertEquals(projektId1, json.getProjektId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.get("/api/projekt/" + projektId1 + "/allAufgabe"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var jsonReader = new JsonMapper().parseJsonPath(res.text());
                    final var allAufgabeId = jsonReader.readContentId();
                    assertEquals(0, allAufgabeId.size());
                    return allAufgabeId;
                });
    }

    public void assertAufgabeNutzer() {
        final var nutzer1 = createRandomNutzer();
        final var nutzer2 = createRandomNutzer();
        final var projekt = createRandomProjekt();

        final var aufgabe = doWithApi(
                (api) -> api.post("/api/aufgabe", RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"titel\":\"Aufgabe C\",\"projektId\":\"%s\"}",
                                projekt.getId()))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.CREATED.value()));
                    final var json = Aufgabe.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertNull(json.getNutzerId());
                    return json;
                });

        doWithApi(
                (api) -> api.get("/api/aufgabe/" + aufgabe.getId() + "/nutzer"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.NOT_FOUND.value()));
                    return null;
                });

        doWithApi(
                (api) -> api.patch("/api/aufgabe/" + aufgabe.getId(), RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, "application/merge-patch+json")
                        .setData(String.format("{\"nutzerId\":\"%s\"}",
                                nutzer1.getId()))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Risiko.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(nutzer1.getId(), json.getNutzerId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.get("/api/aufgabe/" + aufgabe.getId() + "/nutzer"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Nutzer.parseJson(res.text());
                    assertEquals(nutzer1.getId(), json.getId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.patch("/api/aufgabe/" + aufgabe.getId(), RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, "application/merge-patch+json")
                        .setData(String.format("{\"nutzerId\":\"%s\"}",
                                nutzer2.getId()))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Risiko.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(nutzer2.getId(), json.getNutzerId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.get("/api/aufgabe/" + aufgabe.getId() + "/nutzer"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Nutzer.parseJson(res.text());
                    assertEquals(nutzer2.getId(), json.getId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.patch("/api/aufgabe/" + aufgabe.getId(), RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, "application/merge-patch+json")
                        .setData("{\"nutzerId\":null}")),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Risiko.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertNull(json.getNutzerId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.get("/api/aufgabe/" + aufgabe.getId() + "/nutzer"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.NOT_FOUND.value()));
                    return null;
                });
    }
}
