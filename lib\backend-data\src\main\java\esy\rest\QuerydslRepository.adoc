:projectDir: ../../../../../../..
:term: QuerydslRepository
= Spezifikation für eine {term}-Klass<PERSON>

Eine {term}-Klasse realisiert Logik für die REST-Schnittstelle.
QueryDSL hilft dabei, komplexe Abfragen klar, sicher und wartbar zu formulieren – ohne auf unleserliche Query-Annotationen oder Abfragen mit dem`CriteriaBuilder` zurückgreifen zu müssen.

TIP: Siehe
https://docs.spring.io/spring-data/jpa/reference/repositories/core-extensions.html#core.extensions.querydsl[Spring Data JPA Reference Guide]

== Konfiguration

tbd QuerydslPredicateExecutor

== Eigenschaften

Keine

== Operationen

tbd
