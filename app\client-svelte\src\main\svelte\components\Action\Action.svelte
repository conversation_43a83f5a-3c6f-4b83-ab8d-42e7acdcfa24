<script>
  import { createEventDispatcher } from "svelte";
  const dispatch = createEventDispatcher();
  import filterProps from "../filterProps.js";
  
  const props = filterProps(
    [
      "allItem",
      "disabled",
      "label",
      "variant",
      "size",
      "icon",
      "valueGetter"
    ],
    $$props
  );

  export let allItem = []; // Array of action objects: { icon, value, text } or { id, label, icon?, disabled?, divider? }
  export let disabled = false;
  export let label = "Actions";
  export let variant = "primary"; // primary, secondary, outline, ghost
  export let size = "md"; // sm, md, lg
  export let icon = "more_vert"; // Material icon name
  export let valueGetter = undefined; // Function to extract value from item

  let _dropdownVisible = false;
  let _buttonElement;
  let _selectedAction = null;

  // Focus management
  export function focus() {
    _buttonElement.focus();
  }

  // Check if items are primitive (strings) or objects
  $: _primitive = allItem.slice(0, 1).findIndex((e) => typeof e !== "object") !== -1;

  // Process items to normalize structure and keep reference to original
  $: _allItemProcessed = allItem.map((e, index) => itemMapper(e, index));

  function itemMapper(e, index) {
    if (_primitive) {
      return {
        id: e,
        label: e,
        text: e,
        icon: undefined,
        value: e,
        disabled: false,
        originalItem: e,
        originalIndex: index
      };
    } else {
      // Handle both allSpracheItem structure and action structure
      if (e.divider) {
        return { divider: true, originalItem: e, originalIndex: index };
      }

      if (typeof valueGetter === "function") {
        return {
          id: valueGetter(e),
          label: e.text,
          text: e.text,
          icon: e.icon,
          value: e.value,
          disabled: e.disabled || false,
          originalItem: e,
          originalIndex: index
        };
      } else {
        // Handle both structures: { icon, value, text } and { id, label, icon }
        return {
          id: e.id || e.value?.code || e.value,
          label: e.label || e.text,
          text: e.text || e.label,
          icon: e.icon,
          value: e.value || e,
          disabled: e.disabled || false,
          originalItem: e,
          originalIndex: index
        };
      }
    }
  }

  // Handle action click
  async function onActionClick(action) {
    if (action.disabled) return;

    _dropdownVisible = false;
    _selectedAction = action;

    // Dispatch the original item from allItem, not the processed one
    const originalItem = action.originalItem;
    let value;

    if (_primitive) {
      value = originalItem;
    } else {
      if (typeof valueGetter === "function") {
        value = valueGetter(originalItem);
      } else {
        value = originalItem;
      }
    }

    await dispatch("action", {
      action: originalItem,  // Return the original item from allItem
      value: value,          // The extracted value (if valueGetter is used)
      id: action.id          // The processed id for convenience
    });
  }

  // Toggle dropdown
  function toggleDropdown() {
    if (disabled) return;
    _dropdownVisible = !_dropdownVisible;
  }

  // Close dropdown when clicking outside
  function handleClickOutside(event) {
    if (_buttonElement && !_buttonElement.contains(event.target)) {
      _dropdownVisible = false;
    }
  }

  // Handle keyboard navigation
  function handleKeydown(event) {
    if (event.key === "Escape") {
      _dropdownVisible = false;
      _buttonElement.focus();
    } else if (event.key === "Enter" || event.key === " ") {
      event.preventDefault();
      toggleDropdown();
    } else if (event.key === "ArrowDown" && !_dropdownVisible) {
      event.preventDefault();
      _dropdownVisible = true;
    }
  }

  // Handle action keyboard navigation
  function handleActionKeydown(event, action) {
    if (event.key === "Enter" || event.key === " ") {
      event.preventDefault();
      onActionClick(action);
    }
  }

  // Variant classes
  $: buttonClasses = getButtonClasses(variant, size, disabled);
  
  function getButtonClasses(variant, size, disabled) {
    const baseClasses = "inline-flex items-center justify-center font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2";
    
    const variantClasses = {
      primary: "bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500",
      secondary: "bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500",
      outline: "border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500",
      ghost: "text-gray-700 hover:bg-gray-100 focus:ring-gray-500"
    };
    
    const sizeClasses = {
      sm: "px-3 py-1.5 text-sm",
      md: "px-4 py-2 text-sm",
      lg: "px-6 py-3 text-base"
    };
    
    const disabledClasses = disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer";
    
    return `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${disabledClasses}`;
  }
</script>

<svelte:window on:click={handleClickOutside} />

<div class="relative inline-block text-left">
  <button
    bind:this={_buttonElement}
    type="button"
    class={buttonClasses}
    {disabled}
    {...props}
    on:click={toggleDropdown}
    on:keydown={handleKeydown}
    aria-haspopup="true"
    aria-expanded={_dropdownVisible}
  >
    <span class="flex items-center gap-2">
      {#if icon}
        <i class="material-icons text-lg">{icon}</i>
      {/if}
      {label}
    </span>
  </button>

  {#if _dropdownVisible}
    <div
      class="absolute right-0 z-50 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
      role="menu"
      aria-orientation="vertical"
    >
      <div class="py-1" role="none">
        {#each _allItemProcessed as action, index}
          {#if action.divider}
            <div class="border-t border-gray-100 my-1" role="separator"></div>
          {:else}
            <!-- svelte-ignore a11y-click-events-have-key-events -->
            <!-- svelte-ignore a11y-no-static-element-interactions -->
            <div
              class="flex items-center px-4 py-2 text-sm cursor-pointer"
              class:text-gray-700={!action.disabled}
              class:hover:bg-gray-100={!action.disabled}
              class:text-gray-400={action.disabled}
              class:cursor-not-allowed={action.disabled}
              role="menuitem"
              tabindex={action.disabled ? -1 : 0}
              on:click={() => onActionClick(action)}
              on:keydown={(e) => handleActionKeydown(e, action)}
            >
              {#if action.icon}
                <i class="material-icons text-lg mr-3">{action.icon}</i>
              {/if}
              <span>{action.label}</span>
            </div>
          {/if}
        {/each}
      </div>
    </div>
  {/if}
</div>
