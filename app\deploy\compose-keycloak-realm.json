{"enabled": true, "realm": "master", "loginWithEmailAllowed": true, "resetPasswordAllowed": true, "users": [{"id": "fc39e991-6f78-4d33-8721-0a5a5cda6624", "username": "max.<PERSON><PERSON>", "firstName": "Max", "lastName": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "emailVerified": true, "createdTimestamp": 1714213190214, "enabled": true, "totp": false, "credentials": [{"id": "927086bd-e9f4-4174-8a05-f11d87552d0e", "type": "password", "createdDate": 1714233469614, "secretData": "{\"value\":\"IoG48TliPZPu3h//x/94EtWt2Jhgc/rdMf7bG3VUyQu25brsmPeVW1E3dPCDeEAPpBkG4boSJvYmljynO2tIaA==\",\"salt\":\"9Cf+9rKXFuDrEgdZvLXFzQ==\",\"additionalParameters\":{}}", "credentialData": "{\"hashIterations\":210000,\"algorithm\":\"pbkdf2-sha512\",\"additionalParameters\":{}}"}]}, {"id": "ef8014c2-816c-43fd-9907-a6b8c13d45a9", "username": "mia.<PERSON><PERSON>u", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "emailVerified": true, "createdTimestamp": 1714213236795, "enabled": true, "totp": false, "credentials": [{"id": "dae03e58-0404-4641-94db-0fc403ccc3e8", "type": "password", "createdDate": 1714233488011, "secretData": "{\"value\":\"eH+KjW99Ft1L4a7EiTCZC6a+dJZ379HlwBgIKNjqVOao5yuwhudb7SGD+IfZJuhhW0fMkAPKMEBHiGKokAbbbQ==\",\"salt\":\"F3DQmLROq9Q0F2OJC/uuiw==\",\"additionalParameters\":{}}", "credentialData": "{\"hashIterations\":210000,\"algorithm\":\"pbkdf2-sha512\",\"additionalParameters\":{}}"}]}, {"id": "9f85718a-63df-4730-9749-09ae763e433d", "username": "<PERSON><PERSON>.bruck<PERSON>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "bruck<PERSON><PERSON>@gmx.at", "emailVerified": true, "createdTimestamp": 1714213257351, "enabled": true, "totp": false, "credentials": [{"id": "74565932-19ba-4897-b101-5cfe408f9db3", "type": "password", "createdDate": 1714233505224, "secretData": "{\"value\":\"nnQGhfZZaVZzizmssdK2XZFBHVDSAfg8twS3oPguUtHeDIrU2D8kIoo1juxrMewccIk52v3Ddd2aT7lTVemWiw==\",\"salt\":\"JFxhORVCxuoKEMV1WnQgZA==\",\"additionalParameters\":{}}", "credentialData": "{\"hashIterations\":210000,\"algorithm\":\"pbkdf2-sha512\",\"additionalParameters\":{}}"}]}], "clients": [{"enabled": true, "id": "96633142-e38f-4a11-967c-9e1ee52f59a6", "clientId": "2be5ff537772e904ab28", "secret": "47QOgsqVcXk2uXkyMeWsYQnDtU1d3WPC", "clientAuthenticatorType": "client-secret", "redirectUris": ["*"], "webOrigins": ["*"], "protocol": "openid-connect", "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}]}