{"version": 3, "sources": ["Circle.svelte", "Circle2.svelte", "Circle3.svelte", "../../node_modules/svelte-loading-spinners/utils.js", "DoubleBounce.svelte", "GoogleSpin.svelte", "ScaleOut.svelte", "SpinLine.svelte", "Stretch.svelte", "BarLoader.svelte", "Jumper.svelte", "RingLoader.svelte", "SyncLoader.svelte", "Rainbow.svelte", "Firework.svelte", "Pulse.svelte", "Jellyfish.svelte", "Chasing.svelte", "Square.svelte", "Shadow.svelte", "Moon.svelte", "Plane.svelte", "Diamonds.svelte", "Clock.svelte", "Wave.svelte", "<PERSON><PERSON><PERSON>svelte", "ArrowDown.svelte", "ArrowUp.svelte"], "sourcesContent": ["<script>export let color = '#FF3E00';\nexport let unit = 'px';\nexport let duration = '0.75s';\nexport let size = '60';\nexport let pause = false;\n</script>\n\n<div\n\tclass=\"circle\"\n\tclass:pause-animation={pause}\n\tstyle=\"--size: {size}{unit}; --color: {color}; --duration: {duration}\"\n/>\n\n<style>\n\t.circle {\n\t\theight: var(--size);\n\t\twidth: var(--size);\n\t\tborder-color: var(--color) transparent var(--color) var(--color);\n\t\tborder-width: calc(var(--size) / 15);\n\t\tborder-style: solid;\n\t\tborder-image: initial;\n\t\tborder-radius: 50%;\n\t\tanimation: var(--duration) linear 0s infinite normal none running rotate;\n\t}\n\t.pause-animation {\n\t\tanimation-play-state: paused;\n\t}\n\t@keyframes rotate {\n\t\t0% {\n\t\t\ttransform: rotate(0);\n\t\t}\n\t\t100% {\n\t\t\ttransform: rotate(360deg);\n\t\t}\n\t}\n</style>\n", "<script>export let size = '60';\nexport let unit = 'px';\nexport let pause = false;\nexport let colorOuter = '#FF3E00';\nexport let colorCenter = '#40B3FF';\nexport let colorInner = '#676778';\nexport let durationMultiplier = 1;\nexport let durationOuter = `${durationMultiplier * 2}s`;\nexport let durationInner = `${durationMultiplier * 1.5}s`;\nexport let durationCenter = `${durationMultiplier * 3}s`;\n</script>\n\n<div\n\tclass=\"circle\"\n\tclass:pause-animation={pause}\n\tstyle=\"--size: {size}{unit}; --colorInner: {colorInner}; --colorCenter: {colorCenter}; --colorOuter: {colorOuter}; --durationInner: {durationInner}; --durationCenter: {durationCenter}; --durationOuter: {durationOuter};\"\n/>\n\n<style>\n\t.circle {\n\t\twidth: var(--size);\n\t\theight: var(--size);\n\t\tbox-sizing: border-box;\n\t\tposition: relative;\n\t\tborder: 3px solid transparent;\n\t\tborder-top-color: var(--colorOuter);\n\t\tborder-radius: 50%;\n\t\tanimation: circleSpin var(--durationOuter) linear infinite;\n\t}\n\t.circle::before,\n\t.circle::after {\n\t\tcontent: '';\n\t\tbox-sizing: border-box;\n\t\tposition: absolute;\n\t\tborder: 3px solid transparent;\n\t\tborder-radius: 50%;\n\t}\n\t.circle::after {\n\t\tborder-top-color: var(--colorInner);\n\t\ttop: 9px;\n\t\tleft: 9px;\n\t\tright: 9px;\n\t\tbottom: 9px;\n\t\tanimation: circleSpin var(--durationInner) linear infinite;\n\t}\n\t.circle::before {\n\t\tborder-top-color: var(--colorCenter);\n\t\ttop: 3px;\n\t\tleft: 3px;\n\t\tright: 3px;\n\t\tbottom: 3px;\n\t\tanimation: circleSpin var(--durationCenter) linear infinite;\n\t}\n\t.pause-animation,\n\t.pause-animation::after,\n\t.pause-animation::before {\n\t\tanimation-play-state: paused;\n\t}\n\n\t@keyframes circleSpin {\n\t\t0% {\n\t\t\ttransform: rotate(0deg);\n\t\t}\n\t\t100% {\n\t\t\ttransform: rotate(360deg);\n\t\t}\n\t}\n</style>\n", "<script>export let size = '60';\nexport let unit = 'px';\nexport let pause = false;\nexport let ballTopLeft = '#FF3E00';\nexport let ballTopRight = '#F8B334';\nexport let ballBottomLeft = '#40B3FF';\nexport let ballBottomRight = '#676778';\nexport let duration = '1.5s';\n</script>\n\n<div\n\tclass=\"wrapper\"\n\tstyle=\"--size: {size}{unit}; --floatSize: {size}; --ballTopLeftColor: {ballTopLeft}; --ballTopRightColor: {ballTopRight}; --ballBottomLeftColor: {ballBottomLeft}; --ballBottomRightColor: {ballBottomRight}; --duration: {duration};\"\n>\n\t<div class=\"inner\">\n\t\t<div class=\"ball-container\" class:pause-animation={pause}>\n\t\t\t<div class=\"single-ball\">\n\t\t\t\t<div class=\"ball ball-top-left\" class:pause-animation={pause}>&nbsp;</div>\n\t\t\t</div>\n\t\t\t<div class=\"contener_mixte\">\n\t\t\t\t<div class=\"ball ball-top-right\" class:pause-animation={pause}>&nbsp;</div>\n\t\t\t</div>\n\t\t\t<div class=\"contener_mixte\">\n\t\t\t\t<div class=\"ball ball-bottom-left\" class:pause-animation={pause}>&nbsp;</div>\n\t\t\t</div>\n\t\t\t<div class=\"contener_mixte\">\n\t\t\t\t<div class=\"ball ball-bottom-right\" class:pause-animation={pause}>&nbsp;</div>\n\t\t\t</div>\n\t\t</div>\n\t</div>\n</div>\n\n<style>\n\t.wrapper {\n\t\twidth: var(--size);\n\t\theight: var(--size);\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tline-height: 0;\n\t\tbox-sizing: border-box;\n\t}\n\t.inner {\n\t\ttransform: scale(calc(var(--floatSize) / 52));\n\t}\n\t.ball-container {\n\t\tanimation: ballTwo var(--duration) infinite;\n\t\twidth: 44px;\n\t\theight: 44px;\n\t\tflex-shrink: 0;\n\t\tposition: relative;\n\t}\n\t.single-ball {\n\t\twidth: 44px;\n\t\theight: 44px;\n\t\tposition: absolute;\n\t}\n\t.ball {\n\t\twidth: 20px;\n\t\theight: 20px;\n\t\tborder-radius: 50%;\n\t\tposition: absolute;\n\t\tanimation: ballOne var(--duration) infinite ease;\n\t}\n\t.pause-animation {\n\t\tanimation-play-state: paused;\n\t}\n\t.ball-top-left {\n\t\tbackground-color: var(--ballTopLeftColor);\n\t\ttop: 0;\n\t\tleft: 0;\n\t}\n\t.ball-top-right {\n\t\tbackground-color: var(--ballTopRightColor);\n\t\ttop: 0;\n\t\tleft: 24px;\n\t}\n\t.ball-bottom-left {\n\t\tbackground-color: var(--ballBottomLeftColor);\n\t\ttop: 24px;\n\t\tleft: 0;\n\t}\n\t.ball-bottom-right {\n\t\tbackground-color: var(--ballBottomRightColor);\n\t\ttop: 24px;\n\t\tleft: 24px;\n\t}\n\t@keyframes ballOne {\n\t\t0% {\n\t\t\tposition: absolute;\n\t\t}\n\t\t50% {\n\t\t\ttop: 12px;\n\t\t\tleft: 12px;\n\t\t\tposition: absolute;\n\t\t\topacity: 0.5;\n\t\t}\n\t\t100% {\n\t\t\tposition: absolute;\n\t\t}\n\t}\n\t@keyframes ballTwo {\n\t\t0% {\n\t\t\ttransform: rotate(0deg) scale(1);\n\t\t}\n\t\t50% {\n\t\t\ttransform: rotate(360deg) scale(1.3);\n\t\t}\n\t\t100% {\n\t\t\ttransform: rotate(720deg) scale(1);\n\t\t}\n\t}\n</style>\n", "export const durationUnitRegex = /[a-zA-Z]/;\nexport const calculateRgba = (color, opacity) => {\n    if (color[0] === '#') {\n        color = color.slice(1);\n    }\n    if (color.length === 3) {\n        let res = '';\n        color.split('').forEach((c) => {\n            res += c;\n            res += c;\n        });\n        color = res;\n    }\n    const rgbValues = (color.match(/.{2}/g) || []).map((hex) => parseInt(hex, 16)).join(', ');\n    return `rgba(${rgbValues}, ${opacity})`;\n};\nexport const range = (size, startAt = 0) => [...Array(size).keys()].map((i) => i + startAt);\n// export const characterRange = (startChar, endChar) =>\n//   String.fromCharCode(\n//     ...range(\n//       endChar.charCodeAt(0) - startChar.charCodeAt(0),\n//       startChar.charCodeAt(0)\n//     )\n//   );\n// export const zip = (arr, ...arrs) =>\n//   arr.map((val, i) => arrs.reduce((list, curr) => [...list, curr[i]], [val]));\n", "<script>import { range, durationUnitRegex } from './utils';\nexport let color = '#FF3E00';\nexport let unit = 'px';\nexport let duration = '2.1s';\nexport let size = '60';\nexport let pause = false;\nlet durationUnit = duration.match(durationUnitRegex)?.[0] ?? 's';\nlet durationNum = duration.replace(durationUnitRegex, '');\n</script>\n\n<div class=\"wrapper\" style=\"--size: {size}{unit}; --color: {color}\">\n\t{#each range(2, 1) as version}\n\t\t<div\n\t\t\tclass=\"circle\"\n\t\t\tclass:pause-animation={pause}\n\t\t\tstyle=\"animation: {duration} {version === 1\n\t\t\t\t? `${(+durationNum - 0.1) / 2}${durationUnit}`\n\t\t\t\t: `0s`} infinite ease-in-out\"\n\t\t/>\n\t{/each}\n</div>\n\n<style>\n\t.wrapper {\n\t\tposition: relative;\n\t\twidth: var(--size);\n\t\theight: var(--size);\n\t}\n\t.circle {\n\t\tposition: absolute;\n\t\twidth: var(--size);\n\t\theight: var(--size);\n\t\tbackground-color: var(--color);\n\t\tborder-radius: 100%;\n\t\topacity: 0.6;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tanimation-fill-mode: both;\n\t\tanimation-name: bounce !important;\n\t}\n\t.pause-animation {\n\t\tanimation-play-state: paused;\n\t}\n\t@keyframes bounce {\n\t\t0%,\n\t\t100% {\n\t\t\ttransform: scale(0);\n\t\t}\n\t\t50% {\n\t\t\ttransform: scale(1);\n\t\t}\n\t}\n</style>\n", "<script>\n\texport let size = '40px';\n\texport let duration = '3s';\n\texport let pause = false;\n\t$: styles = [`width: ${size}`, `height: ${size}`].join(';');\n</script>\n\n<div\n\tclass=\"spinner spinner--google\"\n\tclass:pause-animation={pause}\n\tstyle=\"--duration: {duration}; {styles}\"\n/>\n\n<style>\n\t* {\n\t\toverflow: hidden;\n\t\tposition: relative;\n\t\ttext-indent: -9999px;\n\t\tdisplay: inline-block;\n\t\tbackground: #f86;\n\t\tborder-radius: 50%;\n\t\ttransform: rotateZ(90deg);\n\t\ttransform-origin: 50% 50%;\n\t\tanimation: plus-loader-background var(--duration) infinite ease-in-out;\n\t}\n\n\t*::after {\n\t\tbackground: #f86;\n\t\tborder-radius: 50% 0 0 50%;\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\tright: 50%;\n\t\ttop: 0;\n\t\twidth: 50%;\n\t\theight: 100%;\n\t\ttransform-origin: 100% 50%;\n\t\tanimation: plus-loader-top var(--duration) infinite linear;\n\t}\n\n\t*::before {\n\t\tbackground: #fc6;\n\t\tborder-radius: 50% 0 0 50%;\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\tright: 50%;\n\t\ttop: 0;\n\t\twidth: 50%;\n\t\theight: 100%;\n\t\ttransform-origin: 100% 50%;\n\t\tanimation: plus-loader-bottom var(--duration) infinite linear;\n\t}\n\t.pause-animation,\n\t.pause-animation::before,\n\t.pause-animation::after {\n\t\tanimation-play-state: paused;\n\t}\n\n\t@keyframes plus-loader-top {\n\t\t2.5% {\n\t\t\tbackground: #f86;\n\t\t\ttransform: rotateY(0deg);\n\t\t\tanimation-timing-function: ease-in;\n\t\t}\n\n\t\t13.75% {\n\t\t\tbackground: #ff430d;\n\t\t\ttransform: rotateY(90deg);\n\t\t\tanimation-timing-function: step-start;\n\t\t}\n\n\t\t13.76% {\n\t\t\tbackground: #ffae0d;\n\t\t\ttransform: rotateY(90deg);\n\t\t\tanimation-timing-function: ease-out;\n\t\t}\n\n\t\t25% {\n\t\t\tbackground: #fc6;\n\t\t\ttransform: rotateY(180deg);\n\t\t}\n\n\t\t27.5% {\n\t\t\tbackground: #fc6;\n\t\t\ttransform: rotateY(180deg);\n\t\t\tanimation-timing-function: ease-in;\n\t\t}\n\n\t\t41.25% {\n\t\t\tbackground: #ffae0d;\n\t\t\ttransform: rotateY(90deg);\n\t\t\tanimation-timing-function: step-start;\n\t\t}\n\n\t\t41.26% {\n\t\t\tbackground: #2cc642;\n\t\t\ttransform: rotateY(90deg);\n\t\t\tanimation-timing-function: ease-out;\n\t\t}\n\n\t\t50% {\n\t\t\tbackground: #6d7;\n\t\t\ttransform: rotateY(0deg);\n\t\t}\n\n\t\t52.5% {\n\t\t\tbackground: #6d7;\n\t\t\ttransform: rotateY(0deg);\n\t\t\tanimation-timing-function: ease-in;\n\t\t}\n\n\t\t63.75% {\n\t\t\tbackground: #2cc642;\n\t\t\ttransform: rotateY(90deg);\n\t\t\tanimation-timing-function: step-start;\n\t\t}\n\n\t\t63.76% {\n\t\t\tbackground: #1386d2;\n\t\t\ttransform: rotateY(90deg);\n\t\t\tanimation-timing-function: ease-out;\n\t\t}\n\n\t\t75% {\n\t\t\tbackground: #4ae;\n\t\t\ttransform: rotateY(180deg);\n\t\t}\n\n\t\t77.5% {\n\t\t\tbackground: #4ae;\n\t\t\ttransform: rotateY(180deg);\n\t\t\tanimation-timing-function: ease-in;\n\t\t}\n\n\t\t91.25% {\n\t\t\tbackground: #1386d2;\n\t\t\ttransform: rotateY(90deg);\n\t\t\tanimation-timing-function: step-start;\n\t\t}\n\n\t\t91.26% {\n\t\t\tbackground: #ff430d;\n\t\t\ttransform: rotateY(90deg);\n\t\t\tanimation-timing-function: ease-in;\n\t\t}\n\n\t\t100% {\n\t\t\tbackground: #f86;\n\t\t\ttransform: rotateY(0deg);\n\t\t\tanimation-timing-function: step-start;\n\t\t}\n\t}\n\n\t@keyframes plus-loader-bottom {\n\t\t0% {\n\t\t\tbackground: #fc6;\n\t\t\tanimation-timing-function: step-start;\n\t\t}\n\n\t\t50% {\n\t\t\tbackground: #fc6;\n\t\t\tanimation-timing-function: step-start;\n\t\t}\n\n\t\t75% {\n\t\t\tbackground: #4ae;\n\t\t\tanimation-timing-function: step-start;\n\t\t}\n\n\t\t100% {\n\t\t\tbackground: #4ae;\n\t\t\tanimation-timing-function: step-start;\n\t\t}\n\t}\n\n\t@keyframes plus-loader-background {\n\t\t0% {\n\t\t\tbackground: #f86;\n\t\t\ttransform: rotateZ(180deg);\n\t\t}\n\n\t\t25% {\n\t\t\tbackground: #f86;\n\t\t\ttransform: rotateZ(180deg);\n\t\t\tanimation-timing-function: step-start;\n\t\t}\n\n\t\t27.5% {\n\t\t\tbackground: #6d7;\n\t\t\ttransform: rotateZ(90deg);\n\t\t}\n\n\t\t50% {\n\t\t\tbackground: #6d7;\n\t\t\ttransform: rotateZ(90deg);\n\t\t\tanimation-timing-function: step-start;\n\t\t}\n\n\t\t52.5% {\n\t\t\tbackground: #6d7;\n\t\t\ttransform: rotateZ(0deg);\n\t\t}\n\n\t\t75% {\n\t\t\tbackground: #6d7;\n\t\t\ttransform: rotateZ(0deg);\n\t\t\tanimation-timing-function: step-start;\n\t\t}\n\n\t\t77.5% {\n\t\t\tbackground: #f86;\n\t\t\ttransform: rotateZ(270deg);\n\t\t}\n\n\t\t100% {\n\t\t\tbackground: #f86;\n\t\t\ttransform: rotateZ(270deg);\n\t\t\tanimation-timing-function: step-start;\n\t\t}\n\t}\n</style>\n", "<script>export let color = '#FF3E00';\nexport let unit = 'px';\nexport let duration = '1s';\nexport let size = '60';\nexport let pause = false;\n</script>\n\n<div\n\tclass=\"wrapper\"\n\tstyle=\"--size: {size}{unit}; --color: {color}; --duration: {duration}; --duration: {duration};\"\n>\n\t<div class=\"circle\" class:pause-animation={pause} />\n</div>\n\n<style>\n\t.wrapper {\n\t\twidth: var(--size);\n\t\theight: var(--size);\n\t}\n\t.circle {\n\t\twidth: var(--size);\n\t\theight: var(--size);\n\t\tbackground-color: var(--color);\n\t\tanimation-duration: var(--duration);\n\t\tborder-radius: 100%;\n\t\tdisplay: inline-block;\n\t\tanimation: scaleOut var(--duration) ease-in-out infinite;\n\t}\n\t.pause-animation {\n\t\tanimation-play-state: paused;\n\t}\n\t@keyframes scaleOut {\n\t\t0% {\n\t\t\ttransform: scale(0);\n\t\t}\n\t\t100% {\n\t\t\ttransform: scale(1);\n\t\t\topacity: 0;\n\t\t}\n\t}\n</style>\n", "<script>export let color = '#FF3E00';\nexport let unit = 'px';\nexport let duration = '4s';\nexport let size = '60';\nexport let stroke = +size / 12 + unit;\nexport let pause = false;\n</script>\n\n<div\n\tclass=\"wrapper\"\n\tstyle=\"--size: {size}{unit}; --color: {color}; --stroke: {stroke}; --floatSize: {size}; --duration: {duration}\"\n>\n\t<div class=\"line\" class:pause-animation={pause} />\n</div>\n\n<style>\n\t.wrapper {\n\t\twidth: var(--size);\n\t\theight: var(--stroke);\n\t\ttransform: scale(calc(var(--floatSize) / 75));\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n\t.line {\n\t\twidth: var(--size);\n\t\theight: var(--stroke);\n\t\tbackground: var(--color);\n\t\tborder-radius: var(--stroke);\n\t\ttransform-origin: center center;\n\t\tanimation: spineLine var(--duration) ease infinite;\n\t}\n\t.pause-animation {\n\t\tanimation-play-state: paused;\n\t}\n\t@keyframes spineLine {\n\t\t0% {\n\t\t\ttransform: rotate(-20deg);\n\t\t\theight: 5px;\n\t\t\twidth: 75px;\n\t\t}\n\t\t5% {\n\t\t\theight: 5px;\n\t\t\twidth: 75px;\n\t\t}\n\t\t30% {\n\t\t\ttransform: rotate(380deg);\n\t\t\theight: 5px;\n\t\t\twidth: 75px;\n\t\t}\n\t\t40% {\n\t\t\ttransform: rotate(360deg);\n\t\t\theight: 5px;\n\t\t\twidth: 75px;\n\t\t}\n\t\t55% {\n\t\t\ttransform: rotate(0deg);\n\t\t\theight: 5px;\n\t\t\twidth: 5px;\n\t\t}\n\t\t65% {\n\t\t\ttransform: rotate(0deg);\n\t\t\theight: 5px;\n\t\t\twidth: 85px;\n\t\t}\n\t\t68% {\n\t\t\ttransform: rotate(0deg);\n\t\t\theight: 5px;\n\t\t}\n\t\t75% {\n\t\t\ttransform: rotate(0deg);\n\t\t\theight: 5px;\n\t\t\twidth: 1px;\n\t\t}\n\t\t78% {\n\t\t\theight: 5px;\n\t\t\twidth: 5px;\n\t\t}\n\t\t90% {\n\t\t\theight: 5px;\n\t\t\twidth: 75px;\n\t\t\ttransform: rotate(0deg);\n\t\t}\n\t\t99%,\n\t\t100% {\n\t\t\theight: 5px;\n\t\t\twidth: 75px;\n\t\t\ttransform: rotate(-20deg);\n\t\t}\n\t}\n</style>\n", "<script>import { range, durationUnitRegex } from './utils';\nexport let color = '#FF3E00';\nexport let unit = 'px';\nexport let duration = '1.2s';\nexport let size = '60';\nexport let pause = false;\nlet durationUnit = duration.match(durationUnitRegex)?.[0] ?? 's';\nlet durationNum = duration.replace(durationUnitRegex, '');\n</script>\n\n<div class=\"wrapper\" style=\"--size: {size}{unit}; --color: {color}; --duration: {duration}\">\n\t{#each range(5, 1) as version}\n\t\t<div\n\t\t\tclass=\"rect\"\n\t\t\tclass:pause-animation={pause}\n\t\t\tstyle=\"animation-delay: {(version - 1) * (+durationNum / 12)}{durationUnit}\"\n\t\t/>\n\t{/each}\n</div>\n\n<style>\n\t.wrapper {\n\t\theight: var(--size);\n\t\twidth: var(--size);\n\t\tdisplay: inline-block;\n\t\ttext-align: center;\n\t\tfont-size: 10px;\n\t}\n\t.rect {\n\t\theight: 100%;\n\t\twidth: 10%;\n\t\tdisplay: inline-block;\n\t\tmargin-right: 4px;\n\t\ttransform: scaleY(0.4);\n\t\tbackground-color: var(--color);\n\t\tanimation: stretch var(--duration) ease-in-out infinite;\n\t}\n\t.pause-animation {\n\t\tanimation-play-state: paused;\n\t}\n\t@keyframes stretch {\n\t\t0%,\n\t\t40%,\n\t\t100% {\n\t\t\ttransform: scaleY(0.4);\n\t\t}\n\t\t20% {\n\t\t\ttransform: scaleY(1);\n\t\t}\n\t}\n</style>\n", "<script>import { calculateRgba, range } from './utils';\nexport let color = '#FF3E00';\nexport let unit = 'px';\nexport let duration = '2.1s';\nexport let size = '60';\nexport let pause = false;\nlet rgba;\n$: rgba = calculateRgba(color, 0.2);\n</script>\n\n<div class=\"wrapper\" style=\"--size: {size}{unit}; --rgba:{rgba}\">\n\t{#each range(2, 1) as version}\n\t\t<div\n\t\t\tclass=\"lines small-lines {version}\"\n\t\t\tclass:pause-animation={pause}\n\t\t\tstyle=\"--color: {color}; --duration: {duration};\"\n\t\t/>\n\t{/each}\n</div>\n\n<style>\n\t.wrapper {\n\t\theight: calc(var(--size) / 15);\n\t\twidth: calc(var(--size) * 2);\n\t\tbackground-color: var(--rgba);\n\t\tposition: relative;\n\t\toverflow: hidden;\n\t\tbackground-clip: padding-box;\n\t}\n\t.lines {\n\t\theight: calc(var(--size) / 15);\n\t\tbackground-color: var(--color);\n\t}\n\n\t.small-lines {\n\t\tposition: absolute;\n\t\toverflow: hidden;\n\t\tbackground-clip: padding-box;\n\t\tdisplay: block;\n\t\tborder-radius: 2px;\n\t\twill-change: left, right;\n\t\tanimation-fill-mode: forwards;\n\t}\n\t.small-lines.\\31 {\n\t\tanimation: var(--duration) cubic-bezier(0.65, 0.815, 0.735, 0.395) 0s infinite normal none\n\t\t\trunning long;\n\t}\n\t.small-lines.\\32 {\n\t\tanimation: var(--duration) cubic-bezier(0.165, 0.84, 0.44, 1) calc((var(--duration) + 0.1) / 2)\n\t\t\tinfinite normal none running short;\n\t}\n\t.pause-animation {\n\t\tanimation-play-state: paused;\n\t}\n\n\t@keyframes long {\n\t\t0% {\n\t\t\tleft: -35%;\n\t\t\tright: 100%;\n\t\t}\n\t\t60% {\n\t\t\tleft: 100%;\n\t\t\tright: -90%;\n\t\t}\n\t\t100% {\n\t\t\tleft: 100%;\n\t\t\tright: -90%;\n\t\t}\n\t}\n\t@keyframes short {\n\t\t0% {\n\t\t\tleft: -200%;\n\t\t\tright: 100%;\n\t\t}\n\t\t60% {\n\t\t\tleft: 107%;\n\t\t\tright: -8%;\n\t\t}\n\t\t100% {\n\t\t\tleft: 107%;\n\t\t\tright: -8%;\n\t\t}\n\t}\n</style>\n", "<script>import { range, durationUnitRegex } from './utils';\nexport let color = '#FF3E00';\nexport let unit = 'px';\nexport let duration = '1s';\nexport let size = '60';\nexport let pause = false;\nlet durationUnit = duration.match(durationUnitRegex)?.[0] ?? 's';\nlet durationNum = duration.replace(durationUnitRegex, '');\n</script>\n\n<div class=\"wrapper\" style=\"--size: {size}{unit}; --color: {color}; --duration: {duration};\">\n\t{#each range(3, 1) as version}\n\t\t<div\n\t\t\tclass=\"circle\"\n\t\t\tclass:pause-animation={pause}\n\t\t\tstyle=\"animation-delay: {(+durationNum / 3) * (version - 1) + durationUnit};\"\n\t\t/>\n\t{/each}\n</div>\n\n<style>\n\t.wrapper {\n\t\twidth: var(--size);\n\t\theight: var(--size);\n\t}\n\t.circle {\n\t\tborder-radius: 100%;\n\t\tanimation-fill-mode: both;\n\t\tposition: absolute;\n\t\topacity: 0;\n\t\twidth: var(--size);\n\t\theight: var(--size);\n\t\tbackground-color: var(--color);\n\t\tanimation: bounce var(--duration) linear infinite;\n\t}\n\t.pause-animation {\n\t\tanimation-play-state: paused;\n\t}\n\t@keyframes bounce {\n\t\t0% {\n\t\t\topacity: 0;\n\t\t\ttransform: scale(0);\n\t\t}\n\t\t5% {\n\t\t\topacity: 1;\n\t\t}\n\t\t100% {\n\t\t\topacity: 0;\n\t\t\ttransform: scale(1);\n\t\t}\n\t}\n</style>\n", "<script>import { range } from './utils';\nexport let color = '#FF3E00';\nexport let unit = 'px';\nexport let duration = '2s';\nexport let size = '60';\nexport let pause = false;\n</script>\n\n<div class=\"wrapper\" style=\"--size: {size}{unit}; --color: {color}; --duration: {duration};\">\n\t{#each range(2, 1) as version}\n\t\t<div class=\"border {version}\" class:pause-animation={pause} />\n\t{/each}\n</div>\n\n<style>\n\t.wrapper {\n\t\tposition: relative;\n\t\twidth: var(--size);\n\t\theight: var(--size);\n\t}\n\t.border {\n\t\tborder-color: var(--color);\n\t\tposition: absolute;\n\t\ttop: 0px;\n\t\tleft: 0px;\n\t\twidth: var(--size);\n\t\theight: var(--size);\n\t\topacity: 0.4;\n\t\tperspective: 800px;\n\t\tborder-width: 6px;\n\t\tborder-style: solid;\n\t\tborder-image: initial;\n\t\tborder-radius: 100%;\n\t}\n\t.border.\\31 {\n\t\tanimation: var(--duration) linear 0s infinite normal none running ringOne;\n\t}\n\t.border.\\32 {\n\t\tanimation: var(--duration) linear 0s infinite normal none running ringTwo;\n\t}\n\t.pause-animation {\n\t\tanimation-play-state: paused;\n\t}\n\n\t@keyframes ringOne {\n\t\t0% {\n\t\t\ttransform: rotateX(0deg) rotateY(0deg) rotateZ(0deg);\n\t\t}\n\t\t100% {\n\t\t\ttransform: rotateX(360deg) rotateY(180deg) rotateZ(360deg);\n\t\t}\n\t}\n\t@keyframes ringTwo {\n\t\t0% {\n\t\t\ttransform: rotateX(0deg) rotateY(0deg) rotateZ(0deg);\n\t\t}\n\t\t100% {\n\t\t\ttransform: rotateX(180deg) rotateY(360deg) rotateZ(360deg);\n\t\t}\n\t}\n</style>\n", "<script>import { range, durationUnitRegex } from './utils';\nexport let color = '#FF3E00';\nexport let unit = 'px';\nexport let duration = '0.6s';\nexport let size = '60';\nexport let pause = false;\nlet durationUnit = duration.match(durationUnitRegex)?.[0] ?? 's';\nlet durationNum = duration.replace(durationUnitRegex, '');\n</script>\n\n<div class=\"wrapper\" style=\"--size:{size}{unit}; --duration: {duration};\">\n\t{#each range(3, 1) as i}\n\t\t<div\n\t\t\tclass=\"dot\"\n\t\t\tclass:pause-animation={pause}\n\t\t\tstyle=\"--dotSize:{+size * 0.25}{unit}; --color:{color}; animation-delay:  {i *\n\t\t\t\t(+durationNum / 10)}{durationUnit};\"\n\t\t/>\n\t{/each}\n</div>\n\n<style>\n\t.wrapper {\n\t\theight: var(--size);\n\t\twidth: var(--size);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\n\t.dot {\n\t\theight: var(--dotSize);\n\t\twidth: var(--dotSize);\n\t\tbackground-color: var(--color);\n\t\tmargin: 2px;\n\t\tdisplay: inline-block;\n\t\tborder-radius: 100%;\n\t\tanimation: sync var(--duration) ease-in-out infinite alternate both running;\n\t}\n\t.pause-animation {\n\t\tanimation-play-state: paused;\n\t}\n\n\t@-webkit-keyframes sync {\n\t\t33% {\n\t\t\t-webkit-transform: translateY(10px);\n\t\t\ttransform: translateY(10px);\n\t\t}\n\t\t66% {\n\t\t\t-webkit-transform: translateY(-10px);\n\t\t\ttransform: translateY(-10px);\n\t\t}\n\t\t100% {\n\t\t\t-webkit-transform: translateY(0);\n\t\t\ttransform: translateY(0);\n\t\t}\n\t}\n\t@keyframes sync {\n\t\t33% {\n\t\t\t-webkit-transform: translateY(10px);\n\t\t\ttransform: translateY(10px);\n\t\t}\n\t\t66% {\n\t\t\t-webkit-transform: translateY(-10px);\n\t\t\ttransform: translateY(-10px);\n\t\t}\n\t\t100% {\n\t\t\t-webkit-transform: translateY(0);\n\t\t\ttransform: translateY(0);\n\t\t}\n\t}\n</style>\n", "<script>export let color = '#FF3E00';\nexport let unit = 'px';\nexport let duration = '3s';\nexport let size = '60';\nexport let pause = false;\n</script>\n\n<div class=\"wrapper\" style=\"--size: {size}{unit}; --color: {color}; --duration: {duration};\">\n\t<div class=\"rainbow\" class:pause-animation={pause} />\n</div>\n\n<style>\n\t.wrapper {\n\t\twidth: var(--size);\n\t\theight: calc(var(--size) / 2);\n\t\toverflow: hidden;\n\t}\n\t.rainbow {\n\t\twidth: var(--size);\n\t\theight: var(--size);\n\t\tborder-left-color: transparent;\n\t\tborder-bottom-color: transparent;\n\t\tborder-top-color: var(--color);\n\t\tborder-right-color: var(--color);\n\t\tbox-sizing: border-box;\n\t\ttransform: rotate(-200deg);\n\t\tborder-radius: 50%;\n\t\tborder-style: solid;\n\t\tanimation: var(--duration) ease-in-out 0s infinite normal none running rotate;\n\t}\n\t.pause-animation {\n\t\tanimation-play-state: paused;\n\t}\n\t@keyframes rotate {\n\t\t0% {\n\t\t\tborder-width: 10px;\n\t\t}\n\t\t25% {\n\t\t\tborder-width: 3px;\n\t\t}\n\t\t50% {\n\t\t\ttransform: rotate(115deg);\n\t\t\tborder-width: 10px;\n\t\t}\n\t\t75% {\n\t\t\tborder-width: 3px;\n\t\t}\n\t\t100% {\n\t\t\tborder-width: 10px;\n\t\t}\n\t}\n</style>\n", "<script>export let color = '#FF3E00';\nexport let unit = 'px';\nexport let duration = '1.25s';\nexport let size = '60';\nexport let pause = false;\n</script>\n\n<div class=\"wrapper\" style=\"--size: {size}{unit}; --color: {color}; --duration: {duration};\">\n\t<div class=\"firework\" class:pause-animation={pause} />\n</div>\n\n<style>\n\t.wrapper {\n\t\twidth: calc(var(--size) * 1.3);\n\t\theight: calc(var(--size) * 1.3);\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n\t.firework {\n\t\tborder: calc(var(--size) / 10) dotted var(--color);\n\t\twidth: var(--size);\n\t\theight: var(--size);\n\t\tborder-radius: 50%;\n\t\tanimation: fire var(--duration) cubic-bezier(0.165, 0.84, 0.44, 1) infinite;\n\t}\n\t.pause-animation {\n\t\tanimation-play-state: paused;\n\t}\n\n\t@keyframes fire {\n\t\t0% {\n\t\t\topacity: 1;\n\t\t\ttransform: scale(0.1);\n\t\t}\n\t\t25% {\n\t\t\topacity: 0.85;\n\t\t}\n\t\t100% {\n\t\t\ttransform: scale(1);\n\t\t\topacity: 0;\n\t\t}\n\t}\n</style>\n", "<script>import { range, durationUnitRegex } from './utils';\nexport let color = '#FF3E00';\nexport let unit = 'px';\nexport let duration = '1.5s';\nexport let size = '60';\nexport let pause = false;\nlet durationUnit = duration.match(durationUnitRegex)?.[0] ?? 's';\nlet durationNum = duration.replace(durationUnitRegex, '');\n</script>\n\n<div class=\"wrapper\" style=\"--size: {size}{unit}; --color: {color}; --duration: {duration}\">\n\t{#each range(3, 0) as version}\n\t\t<div\n\t\t\tclass=\"cube\"\n\t\t\tclass:pause-animation={pause}\n\t\t\tstyle=\"animation-delay: {version * (+durationNum / 10)}{durationUnit}; left: {version *\n\t\t\t\t(+size / 3 + +size / 15) +\n\t\t\t\tunit};\"\n\t\t/>\n\t{/each}\n</div>\n\n<style>\n\t.wrapper {\n\t\tposition: relative;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\twidth: var(--size);\n\t\theight: calc(var(--size) / 2.5);\n\t}\n\t.cube {\n\t\tposition: absolute;\n\t\ttop: 0px;\n\t\twidth: calc(var(--size) / 5);\n\t\theight: calc(var(--size) / 2.5);\n\t\tbackground-color: var(--color);\n\t\tanimation: motion var(--duration) cubic-bezier(0.895, 0.03, 0.685, 0.22) infinite;\n\t}\n\t.pause-animation {\n\t\tanimation-play-state: paused;\n\t}\n\t@keyframes motion {\n\t\t0% {\n\t\t\topacity: 1;\n\t\t}\n\t\t50% {\n\t\t\topacity: 0;\n\t\t}\n\t\t100% {\n\t\t\topacity: 1;\n\t\t}\n\t}\n</style>\n", "<script>import { range, durationUnitRegex } from './utils';\nexport let color = '#FF3E00';\nexport let unit = 'px';\nexport let duration = '2.5s';\nexport let size = '60';\nexport let pause = false;\nlet durationUnit = duration.match(durationUnitRegex)?.[0] ?? 's';\nlet durationNum = duration.replace(durationUnitRegex, '');\n</script>\n\n<div\n\tclass=\"wrapper\"\n\tstyle=\"--size: {size}{unit}; --color: {color}; --motionOne: {-size /\n\t\t5}{unit}; --motionTwo: {+size / 4}{unit}; --motionThree: {-size /\n\t\t5}{unit}; --duration: {duration};\"\n>\n\t{#each range(6, 0) as version}\n\t\t<div\n\t\t\tclass=\"ring\"\n\t\t\tclass:pause-animation={pause}\n\t\t\tstyle=\"animation-delay: {version * (+durationNum / 25)}{durationUnit}; width: {version *\n\t\t\t\t(+size / 6) +\n\t\t\t\tunit}; height: {(version * (+size / 6)) / 2 + unit}; \"\n\t\t/>\n\t{/each}\n</div>\n\n<style>\n\t.wrapper {\n\t\tposition: relative;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\twidth: var(--size);\n\t\theight: var(--size);\n\t}\n\t.ring {\n\t\tposition: absolute;\n\t\tborder: 2px solid var(--color);\n\t\tborder-radius: 50%;\n\t\tbackground-color: transparent;\n\t\tanimation: motion var(--duration) ease infinite;\n\t}\n\t.pause-animation {\n\t\tanimation-play-state: paused;\n\t}\n\t@keyframes motion {\n\t\t0% {\n\t\t\ttransform: translateY(var(--motionOne));\n\t\t}\n\t\t50% {\n\t\t\ttransform: translateY(var(--motionTwo));\n\t\t}\n\t\t100% {\n\t\t\ttransform: translateY(var(--motionThree));\n\t\t}\n\t}\n</style>\n", "<script>import { durationUnitRegex, range } from './utils';\nexport let color = '#FF3E00';\nexport let unit = 'px';\nexport let duration = '2s';\nexport let size = '60';\nexport let pause = false;\nlet durationUnit = duration.match(durationUnitRegex)?.[0] ?? 's';\nlet durationNum = duration.replace(durationUnitRegex, '');\n</script>\n\n<div class=\"wrapper\" style=\"--size: {size}{unit}; --color: {color}; --duration: {duration};\">\n\t<div class=\"spinner\" class:pause-animation={pause}>\n\t\t{#each range(2, 0) as version}\n\t\t\t<div\n\t\t\t\tclass=\"dot\"\n\t\t\t\tclass:pause-animation={pause}\n\t\t\t\tstyle=\"animation-delay: {version === 1\n\t\t\t\t\t? `${+durationNum / 2}${durationUnit}`\n\t\t\t\t\t: '0s'}; {version === 1 ? 'bottom: 0;' : ''} {version === 1 ? 'top: auto;' : ''}\"\n\t\t\t/>\n\t\t{/each}\n\t</div>\n</div>\n\n<style>\n\t.wrapper {\n\t\theight: var(--size);\n\t\twidth: var(--size);\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n\t.spinner {\n\t\theight: var(--size);\n\t\twidth: var(--size);\n\t\tanimation: rotate var(--duration) infinite linear;\n\t}\n\t.dot {\n\t\twidth: 60%;\n\t\theight: 60%;\n\t\tdisplay: inline-block;\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tbackground-color: var(--color);\n\t\tborder-radius: 100%;\n\t\tanimation: bounce var(--duration) infinite ease-in-out;\n\t}\n\t.pause-animation {\n\t\tanimation-play-state: paused;\n\t}\n\n\t@keyframes rotate {\n\t\t100% {\n\t\t\ttransform: rotate(360deg);\n\t\t}\n\t}\n\t@keyframes bounce {\n\t\t0%,\n\t\t100% {\n\t\t\ttransform: scale(0);\n\t\t}\n\t\t50% {\n\t\t\ttransform: scale(1);\n\t\t}\n\t}\n</style>\n", "<script>export let color = '#FF3E00';\nexport let unit = 'px';\nexport let duration = '3s';\nexport let size = '60';\nexport let pause = false;\n</script>\n\n<div\n\tclass=\"square\"\n\tclass:pause-animation={pause}\n\tstyle=\"--size: {size}{unit}; --color: {color}; --duration: {duration};\"\n/>\n\n<style>\n\t.square {\n\t\theight: var(--size);\n\t\twidth: var(--size);\n\t\tbackground-color: var(--color);\n\t\tanimation: squareDelay var(--duration) 0s infinite cubic-bezier(0.09, 0.57, 0.49, 0.9);\n\t\tanimation-fill-mode: both;\n\t\tperspective: 100px;\n\t\tdisplay: inline-block;\n\t}\n\t.pause-animation {\n\t\tanimation-play-state: paused;\n\t}\n\t@keyframes squareDelay {\n\t\t25% {\n\t\t\t-webkit-transform: rotateX(180deg) rotateY(0);\n\t\t\ttransform: rotateX(180deg) rotateY(0);\n\t\t}\n\t\t50% {\n\t\t\t-webkit-transform: rotateX(180deg) rotateY(180deg);\n\t\t\ttransform: rotateX(180deg) rotateY(180deg);\n\t\t}\n\t\t75% {\n\t\t\t-webkit-transform: rotateX(0) rotateY(180deg);\n\t\t\ttransform: rotateX(0) rotateY(180deg);\n\t\t}\n\t\t100% {\n\t\t\t-webkit-transform: rotateX(0) rotateY(0);\n\t\t\ttransform: rotateX(0) rotateY(0);\n\t\t}\n\t}\n</style>\n", "<script>export let color = '#FF3E00';\nexport let unit = 'px';\nexport let duration = '1.7s';\nexport let size = '60';\nexport let pause = false;\n</script>\n\n<div class=\"wrapper\" style=\"--size: {size}{unit}; --color: {color}; --duration: {duration};\">\n\t<div class=\"shadow\" class:pause-animation={pause} />\n</div>\n\n<style>\n\t.wrapper {\n\t\tposition: relative;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\twidth: var(--size);\n\t\theight: var(--size);\n\t}\n\t.shadow {\n\t\tcolor: var(--color);\n\t\tfont-size: var(--size);\n\t\toverflow: hidden;\n\t\twidth: var(--size);\n\t\theight: var(--size);\n\t\tborder-radius: 50%;\n\t\tmargin: 28px auto;\n\t\tposition: relative;\n\t\ttransform: translateZ(0);\n\t\tanimation: load var(--duration) infinite ease, round var(--duration) infinite ease;\n\t}\n\t.pause-animation {\n\t\tanimation-play-state: paused;\n\t}\n\t@keyframes load {\n\t\t0% {\n\t\t\tbox-shadow: 0 -0.83em 0 -0.4em, 0 -0.83em 0 -0.42em, 0 -0.83em 0 -0.44em, 0 -0.83em 0 -0.46em,\n\t\t\t\t0 -0.83em 0 -0.477em;\n\t\t}\n\t\t5%,\n\t\t95% {\n\t\t\tbox-shadow: 0 -0.83em 0 -0.4em, 0 -0.83em 0 -0.42em, 0 -0.83em 0 -0.44em, 0 -0.83em 0 -0.46em,\n\t\t\t\t0 -0.83em 0 -0.477em;\n\t\t}\n\t\t10%,\n\t\t59% {\n\t\t\tbox-shadow: 0 -0.83em 0 -0.4em, -0.087em -0.825em 0 -0.42em, -0.173em -0.812em 0 -0.44em,\n\t\t\t\t-0.256em -0.789em 0 -0.46em, -0.297em -0.775em 0 -0.477em;\n\t\t}\n\t\t20% {\n\t\t\tbox-shadow: 0 -0.83em 0 -0.4em, -0.338em -0.758em 0 -0.42em, -0.555em -0.617em 0 -0.44em,\n\t\t\t\t-0.671em -0.488em 0 -0.46em, -0.749em -0.34em 0 -0.477em;\n\t\t}\n\t\t38% {\n\t\t\tbox-shadow: 0 -0.83em 0 -0.4em, -0.377em -0.74em 0 -0.42em, -0.645em -0.522em 0 -0.44em,\n\t\t\t\t-0.775em -0.297em 0 -0.46em, -0.82em -0.09em 0 -0.477em;\n\t\t}\n\t\t100% {\n\t\t\tbox-shadow: 0 -0.83em 0 -0.4em, 0 -0.83em 0 -0.42em, 0 -0.83em 0 -0.44em, 0 -0.83em 0 -0.46em,\n\t\t\t\t0 -0.83em 0 -0.477em;\n\t\t}\n\t}\n\t@keyframes round {\n\t\t0% {\n\t\t\ttransform: rotate(0deg);\n\t\t}\n\t\t100% {\n\t\t\ttransform: rotate(360deg);\n\t\t}\n\t}\n</style>\n", "<script>export let color = '#FF3E00';\nexport let unit = 'px';\nexport let duration = '0.6s';\nexport let size = '60';\nexport let pause = false;\nlet moonSize = +size / 7;\nlet top = +size / 2 - moonSize / 2;\n</script>\n\n<div\n\tclass=\"wrapper\"\n\tclass:pause-animation={pause}\n\tstyle=\"--size: {size}{unit}; --color: {color}; --moonSize: {top}{unit}; --duration: {duration};\"\n>\n\t<div class=\"circle-one\" class:pause-animation={pause} />\n\t<div class=\"circle-two\" class:pause-animation={pause} />\n</div>\n\n<style>\n\t.wrapper {\n\t\theight: var(--size);\n\t\twidth: var(--size);\n\t\tborder-radius: 100%;\n\t\tanimation: moonStretchDelay var(--duration) 0s infinite linear;\n\t\tanimation-fill-mode: forwards;\n\t\tposition: relative;\n\t}\n\t.circle-one {\n\t\ttop: var(--moonSize);\n\t\tbackground-color: var(--color);\n\t\twidth: calc(var(--size) / 7);\n\t\theight: calc(var(--size) / 7);\n\t\tborder-radius: 100%;\n\t\tanimation: moonStretchDelay var(--duration) 0s infinite linear;\n\t\tanimation-fill-mode: forwards;\n\t\topacity: 0.8;\n\t\tposition: absolute;\n\t}\n\t.circle-two {\n\t\topacity: 0.1;\n\t\tborder: calc(var(--size) / 7) solid var(--color);\n\t\theight: var(--size);\n\t\twidth: var(--size);\n\t\tborder-radius: 100%;\n\t\tbox-sizing: border-box;\n\t}\n\t.pause-animation {\n\t\tanimation-play-state: paused;\n\t}\n\t@keyframes moonStretchDelay {\n\t\t100% {\n\t\t\ttransform: rotate(360deg);\n\t\t}\n\t}\n</style>\n", "<script>import { calculateRgba } from './utils';\nexport let color = '#FF3E00';\nexport let unit = 'px';\nexport let duration = '1.3s';\nexport let size = '60';\nexport let pause = false;\nlet rgba;\n$: rgba = calculateRgba(color, 0.6);\n</script>\n\n<div\n\tclass=\"wrapper\"\n\tstyle=\"--size: {size}{unit}; --color: {color}; --rgba: {rgba}; --duration: {duration};\"\n>\n\t<div class=\"spinner-inner\" class:pause-animation={pause}>\n\t\t<div id=\"top\" class=\"mask\">\n\t\t\t<div class=\"plane\" />\n\t\t</div>\n\t\t<div id=\"middle\" class=\"mask\">\n\t\t\t<div class=\"plane\" />\n\t\t</div>\n\t\t<div id=\"bottom\" class=\"mask\">\n\t\t\t<div class=\"plane\" />\n\t\t</div>\n\t</div>\n</div>\n\n<style>\n\t.wrapper {\n\t\theight: var(--size);\n\t\twidth: var(--size);\n\t\tposition: relative;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n\t.wrapper * {\n\t\tline-height: 0;\n\t\tbox-sizing: border-box;\n\t}\n\t.spinner-inner {\n\t\theight: var(--size);\n\t\twidth: var(--size);\n\t\ttransform: scale(calc(var(--size) / 70));\n\t}\n\n\t.mask {\n\t\tposition: absolute;\n\t\tborder-radius: 2px;\n\t\toverflow: hidden;\n\t\tperspective: 1000;\n\t\tbackface-visibility: hidden;\n\t}\n\n\t.plane {\n\t\tbackground: var(--color);\n\t\twidth: 400%;\n\t\theight: 100%;\n\t\tposition: absolute;\n\t\tz-index: 100;\n\t\tperspective: 1000;\n\t\tbackface-visibility: hidden;\n\t}\n\n\t#top .plane {\n\t\tz-index: 2000;\n\t\tanimation: trans1 var(--duration) ease-in infinite 0s backwards;\n\t}\n\t#middle .plane {\n\t\ttransform: translate3d(0px, 0, 0);\n\t\tbackground: var(--rgba);\n\t\tanimation: trans2 var(--duration) linear infinite calc(var(--duration) / 4) backwards;\n\t}\n\t#bottom .plane {\n\t\tz-index: 2000;\n\t\tanimation: trans3 var(--duration) ease-out infinite calc(var(--duration) / 2) backwards;\n\t}\n\t#top {\n\t\twidth: 53px;\n\t\theight: 20px;\n\t\tleft: 20px;\n\t\ttop: 5px;\n\t\ttransform: skew(-15deg, 0);\n\t\tz-index: 100;\n\t}\n\t#middle {\n\t\twidth: 33px;\n\t\theight: 20px;\n\t\tleft: 20px;\n\t\ttop: 21px;\n\t\ttransform: skew(-15deg, 40deg);\n\t}\n\t#bottom {\n\t\twidth: 53px;\n\t\theight: 20px;\n\t\ttop: 35px;\n\t\ttransform: skew(-15deg, 0);\n\t}\n\n\t.pause-animation .plane {\n\t\tanimation-play-state: paused;\n\t}\n\n\t@keyframes trans1 {\n\t\tfrom {\n\t\t\ttransform: translate3d(53px, 0, 0);\n\t\t}\n\t\tto {\n\t\t\ttransform: translate3d(-250px, 0, 0);\n\t\t}\n\t}\n\t@keyframes trans2 {\n\t\tfrom {\n\t\t\ttransform: translate3d(-160px, 0, 0);\n\t\t}\n\t\tto {\n\t\t\ttransform: translate3d(53px, 0, 0);\n\t\t}\n\t}\n\t@keyframes trans3 {\n\t\tfrom {\n\t\t\ttransform: translate3d(53px, 0, 0);\n\t\t}\n\t\tto {\n\t\t\ttransform: translate3d(-220px, 0, 0);\n\t\t}\n\t}\n</style>\n", "<script>export let color = '#FF3E00';\nexport let unit = 'px';\nexport let duration = '1.5s';\nexport let size = '60';\nexport let pause = false;\n</script>\n\n<span\n\tstyle=\"--size: {size}{unit}; --color:{color}; --duration: {duration};\"\n\tclass:pause-animation={pause}\n>\n\t<div />\n\t<div />\n\t<div />\n</span>\n\n<style>\n\tspan {\n\t\twidth: var(--size);\n\t\theight: calc(var(--size) / 4);\n\t\tposition: relative;\n\t\tdisplay: block;\n\t}\n\tdiv {\n\t\twidth: calc(var(--size) / 4);\n\t\theight: calc(var(--size) / 4);\n\t\tposition: absolute;\n\t\tleft: 0%;\n\t\ttop: 0;\n\t\tborder-radius: 2px;\n\t\tbackground: var(--color);\n\t\ttransform: translateX(-50%) rotate(45deg) scale(0);\n\t\tanimation: diamonds var(--duration) linear infinite;\n\t}\n\tdiv:nth-child(1) {\n\t\tanimation-delay: calc(var(--duration) * 2 / 3 * -1);\n\t}\n\tdiv:nth-child(2) {\n\t\tanimation-delay: calc(var(--duration) * 2 / 3 * -2);\n\t}\n\tdiv:nth-child(3) {\n\t\tanimation-delay: calc(var(--duration) * 2 / 3 * -3);\n\t}\n\t.pause-animation div {\n\t\tanimation-play-state: paused;\n\t}\n\n\t@keyframes diamonds {\n\t\t50% {\n\t\t\tleft: 50%;\n\t\t\ttransform: translateX(-50%) rotate(45deg) scale(1);\n\t\t}\n\t\t100% {\n\t\t\tleft: 100%;\n\t\t\ttransform: translateX(-50%) rotate(45deg) scale(0);\n\t\t}\n\t}\n</style>\n", "<script>export let color = '#FF3E00';\nexport let unit = 'px';\nexport let duration = '8s';\nexport let size = '60';\nexport let pause = false;\n</script>\n\n<div\n\tstyle=\"--size: {size}{unit}; --color:{color}; --duration:{duration}\"\n\tclass:pause-animation={pause}\n/>\n\n<style>\n\tdiv {\n\t\tposition: relative;\n\t\twidth: var(--size);\n\t\theight: var(--size);\n\t\tbackground-color: transparent;\n\t\tbox-shadow: inset 0px 0px 0px 2px var(--color);\n\t\tborder-radius: 50%;\n\t}\n\tdiv::before,\n\tdiv::after {\n\t\tposition: absolute;\n\t\tcontent: '';\n\t\tbackground-color: var(--color);\n\t}\n\tdiv::after {\n\t\twidth: calc(var(--size) / 2.4);\n\t\theight: 2px;\n\t\ttop: calc(var(--size) / 2);\n\t\tleft: calc(var(--size) / 2);\n\t\ttransform-origin: 1px 1px;\n\t\tanimation: rotate calc(var(--duration) / 4) linear infinite;\n\t}\n\tdiv::before {\n\t\twidth: calc(var(--size) / 3);\n\t\theight: 2px;\n\t\ttop: calc((var(--size) / 2));\n\t\tleft: calc((var(--size) / 2));\n\t\ttransform-origin: 1px 1px;\n\t\tanimation: rotate var(--duration) linear infinite;\n\t}\n\t.pause-animation,\n\t.pause-animation::before,\n\t.pause-animation::after {\n\t\tanimation-play-state: paused;\n\t}\n\t@keyframes rotate {\n\t\t100% {\n\t\t\ttransform: rotate(360deg);\n\t\t}\n\t}\n</style>\n", "<script>import { range, durationUnitRegex } from './utils';\nexport let color = '#FF3E00';\nexport let unit = 'px';\nexport let duration = '1.25s';\nexport let size = '60';\nexport let pause = false;\nlet durationUnit = duration.match(durationUnitRegex)?.[0] ?? 's';\nlet durationNum = duration.replace(durationUnitRegex, '');\n</script>\n\n<div class=\"wrapper\" style=\"--size: {size}{unit}; --color: {color}; --duration: {duration};\">\n\t{#each range(10, 0) as version}\n\t\t<div\n\t\t\tclass=\"bar\"\n\t\t\tclass:pause-animation={pause}\n\t\t\tstyle=\"left: {version * (+size / 5 + (+size / 15 - +size / 100)) +\n\t\t\t\tunit}; animation-delay: {version * (+durationNum / 8.3)}{durationUnit};\"\n\t\t/>\n\t{/each}\n</div>\n\n<style>\n\t.wrapper {\n\t\tposition: relative;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\twidth: calc(var(--size) * 2.5);\n\t\theight: var(--size);\n\t\toverflow: hidden;\n\t}\n\t.bar {\n\t\tposition: absolute;\n\t\ttop: calc(var(--size) / 10);\n\t\twidth: calc(var(--size) / 5);\n\t\theight: calc(var(--size) / 10);\n\t\tmargin-top: calc(var(--size) - var(--size) / 10);\n\t\ttransform: skewY(0deg);\n\t\tbackground-color: var(--color);\n\t\tanimation: motion var(--duration) ease-in-out infinite;\n\t}\n\t.pause-animation {\n\t\tanimation-play-state: paused;\n\t}\n\t@keyframes motion {\n\t\t25% {\n\t\t\ttransform: skewY(25deg);\n\t\t}\n\t\t50% {\n\t\t\theight: 100%;\n\t\t\tmargin-top: 0;\n\t\t}\n\t\t75% {\n\t\t\ttransform: skewY(-25deg);\n\t\t}\n\t}\n</style>\n", "<script>import { range, durationUnitRegex, calculateRgba } from './utils';\nexport let color = '#FF3E00';\nexport let unit = 'px';\nexport let duration = '1s';\nexport let size = '60';\nexport let pause = false;\nlet durationUnit = duration.match(durationUnitRegex)?.[0] ?? 's';\nlet durationNum = duration.replace(durationUnitRegex, '');\nlet rgba;\n$: rgba = calculateRgba(color, 1);\n</script>\n\n<span\n\tclass=\"wrapper\"\n\tstyle=\"--size: {size}{unit}; --color: {color}; --rgba: {rgba}; --duration: {duration}\"\n>\n\t{#each range(2, 1) as version}\n\t\t<span\n\t\t\tclass=\"circle\"\n\t\t\tclass:pause-animation={pause}\n\t\t\tstyle=\"animation-delay: {version === 1 ? '-1s' : '0s'}; animation-duration: {2 /\n\t\t\t\t+durationNum +\n\t\t\t\tdurationUnit};\"\n\t\t/>\n\t{/each}\n</span>\n\n<style>\n\t.wrapper {\n\t\tdisplay: inherit;\n\t\tposition: relative;\n\t\twidth: var(--size);\n\t\theight: var(--size);\n\t}\n\t.circle {\n\t\tposition: absolute;\n\t\twidth: var(--size);\n\t\theight: var(--size);\n\t\tborder: thick solid var(--rgba);\n\t\tborder-radius: 50%;\n\t\topacity: 1;\n\t\ttop: 0px;\n\t\tleft: 0px;\n\t\tanimation-fill-mode: both;\n\t\tanimation-iteration-count: infinite;\n\t\tanimation-timing-function: cubic-bezier(0.165, 0.84, 0.44, 1), cubic-bezier(0.3, 0.61, 0.355, 1);\n\t\tanimation-direction: normal, normal;\n\t\tanimation-fill-mode: none, none;\n\t\tanimation-play-state: running, running;\n\t\tanimation-name: puff-1, puff-2;\n\t\tbox-sizing: border-box;\n\t}\n\t.pause-animation {\n\t\tanimation-play-state: paused;\n\t}\n\t@keyframes puff-1 {\n\t\t0% {\n\t\t\ttransform: scale(0);\n\t\t}\n\t\t100% {\n\t\t\ttransform: scale(1);\n\t\t}\n\t}\n\t@keyframes puff-2 {\n\t\t0% {\n\t\t\topacity: 1;\n\t\t}\n\t\t100% {\n\t\t\topacity: 0;\n\t\t}\n\t}\n</style>\n", "<script>export let color = '#FF3E00';\nexport let unit = 'px';\nexport let duration = '0.4s';\nexport let size = '15';\nexport let pause = false;\n</script>\n\n<div\n\tclass=\"wrapper\"\n\tclass:pause-animation={pause}\n\tstyle=\"--size: {size}{unit}; --color: {color}; --duration: {duration};\"\n/>\n\n<style>\n\t.wrapper {\n\t\twidth: var(--size);\n\t\theight: calc(var(--size) * 1.5);\n\t\tmargin-left: var(--size);\n\t\tbackground: var(--color);\n\t\tdisplay: inline-block;\n\t\tposition: relative;\n\t\tbox-sizing: border-box;\n\t\tanimation: bump var(--duration) ease-in infinite alternate;\n\t}\n\t.wrapper::after {\n\t\tcontent: '';\n\t\tbox-sizing: border-box;\n\t\tleft: 50%;\n\t\ttop: 100%;\n\t\ttransform: translate(-50%, 0);\n\t\tposition: absolute;\n\t\tborder: var(--size) solid transparent;\n\t\tborder-top-color: var(--color);\n\t}\n\n\t.pause-animation {\n\t\tanimation-play-state: paused;\n\t}\n\t@keyframes bump {\n\t\t0% {\n\t\t\ttransform: translate(-50%, 5px);\n\t\t}\n\t\t100% {\n\t\t\ttransform: translate(-50%, -5px);\n\t\t}\n\t}\n</style>\n", "<script>export let color = '#FF3E00';\nexport let unit = 'px';\nexport let duration = '0.4s';\nexport let size = '15';\nexport let pause = false;\n</script>\n\n<div\n\tclass=\"wrapper\"\n\tclass:pause-animation={pause}\n\tstyle=\"--size: {size}{unit}; --color: {color}; --duration: {duration};\"\n/>\n\n<style>\n\t.wrapper {\n\t\twidth: var(--size);\n\t\theight: calc(var(--size) * 1.5);\n\t\tmargin-left: var(--size);\n\t\tmargin-top: var(--size);\n\t\tbackground: var(--color);\n\t\tdisplay: inline-block;\n\t\tposition: relative;\n\t\tbox-sizing: border-box;\n\t\tanimation: bump var(--duration) ease-in infinite alternate;\n\t}\n\t.wrapper::after {\n\t\tcontent: '';\n\t\tbox-sizing: border-box;\n\t\tleft: 50%;\n\t\tbottom: 100%;\n\t\ttransform: translate(-50%, 0);\n\t\tposition: absolute;\n\t\tborder: var(--size) solid transparent;\n\t\tborder-bottom-color: var(--color);\n\t}\n\n\t.pause-animation {\n\t\tanimation-play-state: paused;\n\t}\n\t@keyframes bump {\n\t\t0% {\n\t\t\ttransform: translate(-50%, 5px);\n\t\t}\n\t\t100% {\n\t\t\ttransform: translate(-50%, -5px);\n\t\t}\n\t}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAAmB,QAAK,KAAA,SAAA,SAAA,GAAG,SAAS;MACzB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,WAAQ,KAAA,SAAA,YAAA,GAAG,OAAO;MAClB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,QAAK,KAAA,SAAA,SAAA,GAAG,KAAK;;;;;;gCAMP,KAAI,KAAA,EAAA,GAAE,KAAI,KAAA,EAAA,cAAa,MAAK,KAAA,EAAA,iBAAgB,SAAQ,KAAA,EAAA,EAAA;;iCAD7C,MAAK,EAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;MCTV,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACnB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,QAAK,KAAA,SAAA,SAAA,GAAG,KAAK;MACb,aAAU,KAAA,SAAA,cAAA,GAAG,SAAS;MACtB,cAAW,KAAA,SAAA,eAAA,GAAG,SAAS;MACvB,aAAU,KAAA,SAAA,cAAA,GAAG,SAAS;MACtB,qBAAkB,KAAA,SAAA,sBAAA,GAAG,CAAC;MACtB,gBAAa,KAAA,SAAA,iBAAA,IAAA,MAAA,GAAM,mBAAkB,IAAG,CAAC,GAAA;MACzC,gBAAa,KAAA,SAAA,iBAAA,IAAA,MAAA,GAAM,mBAAkB,IAAG,GAAG,GAAA;MAC3C,iBAAc,KAAA,SAAA,kBAAA,IAAA,MAAA,GAAM,mBAAkB,IAAG,CAAC,GAAA;;;;;;gCAMpC,KAAI,KAAA,EAAA,GAAE,KAAI,KAAA,EAAA,mBAAkB,WAAU,KAAA,EAAA,oBAAmB,YAAW,KAAA,EAAA,mBAAkB,WAAU,KAAA,EAAA,sBAAqB,cAAa,KAAA,EAAA,uBAAsB,eAAc,KAAA,EAAA,sBAAqB,cAAa,KAAA,EAAA,GAAA;;iCADjM,MAAK,EAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MCdV,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACnB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,QAAK,KAAA,SAAA,SAAA,GAAG,KAAK;MACb,cAAW,KAAA,SAAA,eAAA,GAAG,SAAS;MACvB,eAAY,KAAA,SAAA,gBAAA,GAAG,SAAS;MACxB,iBAAc,KAAA,SAAA,kBAAA,GAAG,SAAS;MAC1B,kBAAe,KAAA,SAAA,mBAAA,GAAG,SAAS;MAC3B,WAAQ,KAAA,SAAA,YAAA,GAAG,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;gCAKX,KAAI,KAAA,EAAA,GAAE,KAAI,KAAA,EAAA,kBAAiB,KAAI,KAAA,EAAA,yBAAwB,YAAW,KAAA,EAAA,0BAAyB,aAAY,KAAA,EAAA,4BAA2B,eAAc,KAAA,EAAA,6BAA4B,gBAAe,KAAA,EAAA,iBAAgB,SAAQ,KAAA,EAAA,GAAA;;;;;;;;kCAG/K,MAAK,EAAA;kCAEC,MAAK,EAAA;kCAGJ,MAAK,EAAA;kCAGH,MAAK,EAAA;kCAGJ,MAAK,EAAA;;;;;;;;;;;;;;;;;;AC1B7D,IAAM,oBAAoB;AAC1B,IAAM,gBAAgB,CAAC,OAAO,YAAY;AAC7C,MAAI,MAAM,CAAC,MAAM,KAAK;AAClB,YAAQ,MAAM,MAAM,CAAC;AAAA,EACzB;AACA,MAAI,MAAM,WAAW,GAAG;AACpB,QAAI,MAAM;AACV,UAAM,MAAM,EAAE,EAAE,QAAQ,CAAC,MAAM;AAC3B,aAAO;AACP,aAAO;AAAA,IACX,CAAC;AACD,YAAQ;AAAA,EACZ;AACA,QAAM,aAAa,MAAM,MAAM,OAAO,KAAK,CAAC,GAAG,IAAI,CAAC,QAAQ,SAAS,KAAK,EAAE,CAAC,EAAE,KAAK,IAAI;AACxF,SAAO,QAAQ,SAAS,KAAK,OAAO;AACxC;AACO,IAAM,QAAQ,CAAC,MAAM,UAAU,MAAM,CAAC,GAAG,MAAM,IAAI,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,MAAM,IAAI,OAAO;;;;;;;;;;;;;;;MCf/E,QAAK,KAAA,SAAA,SAAA,GAAG,SAAS;MACjB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,WAAQ,KAAA,SAAA,YAAA,GAAG,MAAM;MACjB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,QAAK,KAAA,SAAA,SAAA,GAAG,KAAK;MACpB,iBAAe,cAAQ,EAAC,MAAM,iBAAiB,MAAhC,mBAAoC,OAAM;MACzD,cAAc,SAAQ,EAAC,QAAQ,mBAAmB,EAAE;;;qBAIhD,MAAM,GAAG,CAAC,GAAA,OAAA,CAAAA,WAAK,YAAO;;;;;;uCAIR,SAAQ,KAAA,EAAA,IAAA,cAAA,IAAG,OAAO,GAAK,CAAC,IAAA,IAAA,CACnC,cAAc,OAAO,CAAC,GAAG,YAAY,KAAA,IAAA,uBAAA;;mCAFtB,MAAK,EAAA,EAAA;;;;;;kDAJM,KAAI,KAAA,EAAA,GAAE,KAAI,KAAA,EAAA,cAAa,MAAK,KAAA,EAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;MCTrD,OAAI,KAAA,SAAA,QAAA,GAAG,MAAM;MACb,WAAQ,KAAA,SAAA,YAAA,GAAG,IAAI;MACf,QAAK,KAAA,SAAA,SAAA,GAAG,KAAK;;QACrB,QAAM;gBAAc,KAAI,CAAA;iBAAe,KAAI,CAAA;MAAI,KAAK,GAAG,CAAA;;;;;;;;;oCAMtC,SAAQ,KAAA,EAAA,KAAA,IAAI,MAAM,KAAA,EAAA,EAAA;;iCADf,MAAK,EAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;MCTV,QAAK,KAAA,SAAA,SAAA,GAAG,SAAS;MACzB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,WAAQ,KAAA,SAAA,YAAA,GAAG,IAAI;MACf,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,QAAK,KAAA,SAAA,SAAA,GAAG,KAAK;;;;;;;gCAKP,KAAI,KAAA,EAAA,GAAE,KAAI,KAAA,EAAA,cAAa,MAAK,KAAA,EAAA,iBAAgB,SAAQ,KAAA,EAAA,iBAAgB,SAAQ,KAAA,EAAA,GAAA;;;iCAEjD,MAAK,EAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;MCX9B,QAAK,KAAA,SAAA,SAAA,GAAG,SAAS;MACzB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,WAAQ,KAAA,SAAA,YAAA,GAAG,IAAI;MACf,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,SAAM,KAAA,SAAA,UAAA,IAAA,MAAA,CAAI,KAAI,IAAG,KAAK,KAAI,CAAA;MAC1B,QAAK,KAAA,SAAA,SAAA,GAAG,KAAK;;;;;;;gCAKP,KAAI,KAAA,EAAA,GAAE,KAAI,KAAA,EAAA,cAAa,MAAK,KAAA,EAAA,eAAc,OAAM,KAAA,EAAA,kBAAiB,KAAI,KAAA,EAAA,iBAAgB,SAAQ,KAAA,EAAA,EAAA;;;iCAEpE,MAAK,EAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MCXpC,QAAK,KAAA,SAAA,SAAA,GAAG,SAAS;MACjB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,WAAQ,KAAA,SAAA,YAAA,GAAG,MAAM;MACjB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,QAAK,KAAA,SAAA,SAAA,GAAG,KAAK;MACpB,iBAAe,cAAQ,EAAC,MAAM,iBAAiB,MAAhC,mBAAoC,OAAM;MACzD,cAAc,SAAQ,EAAC,QAAQ,mBAAmB,EAAE;;;qBAIhD,MAAM,GAAG,CAAC,GAAA,OAAA,CAAAC,WAAK,YAAO;;;;;;kDAID,OAAO,IAAG,MAAC,CAAM,cAAc,GAAE,GAAG,gBAAY,EAAA,EAAA;;mCADnD,MAAK,EAAA,EAAA;;;;;;kDAJM,KAAI,KAAA,EAAA,GAAE,KAAI,KAAA,EAAA,cAAa,MAAK,KAAA,EAAA,iBAAgB,SAAQ,KAAA,EAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;MCT9E,QAAK,KAAA,SAAA,SAAA,GAAG,SAAS;MACjB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,WAAQ,KAAA,SAAA,YAAA,GAAG,MAAM;MACjB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,QAAK,KAAA,SAAA,SAAA,GAAG,KAAK;MACpB,OAAI,eAAA;;;;UACL,MAAO,cAAc,MAAK,GAAE,GAAG,CAAA;;;;;;qBAI1B,MAAM,GAAG,CAAC,GAAA,OAAA,CAAAC,WAAK,YAAO;;;;;+DAED,OAAO,KAAA,EAAA,IAAA,kBAAA,SAAA,EAAA;qCAEhB,MAAK,KAAA,EAAA,iBAAgB,SAAQ,KAAA,EAAA,GAAA;;mCADvB,MAAK,EAAA,EAAA;;;;;;kDAJM,KAAI,KAAA,EAAA,GAAE,KAAI,KAAA,EAAA,YAAA,IAAW,IAAI,KAAA,EAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;MCTnD,QAAK,KAAA,SAAA,SAAA,GAAG,SAAS;MACjB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,WAAQ,KAAA,SAAA,YAAA,GAAG,IAAI;MACf,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,QAAK,KAAA,SAAA,SAAA,GAAG,KAAK;MACpB,iBAAe,cAAQ,EAAC,MAAM,iBAAiB,MAAhC,mBAAoC,OAAM;MACzD,cAAc,SAAQ,EAAC,QAAQ,mBAAmB,EAAE;;;qBAIhD,MAAM,GAAG,CAAC,GAAA,OAAA,CAAAC,WAAK,YAAO;;;;;;8CAIA,cAAc,KAAC,IAAK,OAAO,IAAG,KAAK,YAAY,GAAA;;mCADnD,MAAK,EAAA,EAAA;;;;;;kDAJM,KAAI,KAAA,EAAA,GAAE,KAAI,KAAA,EAAA,cAAa,MAAK,KAAA,EAAA,iBAAgB,SAAQ,KAAA,EAAA,GAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;MCT9E,QAAK,KAAA,SAAA,SAAA,GAAG,SAAS;MACjB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,WAAQ,KAAA,SAAA,YAAA,GAAG,IAAI;MACf,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,QAAK,KAAA,SAAA,SAAA,GAAG,KAAK;;;qBAIhB,MAAM,GAAG,CAAC,GAAA,OAAA,CAAAC,WAAK,YAAO;;;wEACR,OAAO,KAAA,EAAA,IAAA,kBAAA,SAAA,EAAA,GAAA,CAAA,OAAA,EAAA,mBAA0B,MAAK,EAAA,EAAA,GAAA,kBAAA;;;;kDAFvB,KAAI,KAAA,EAAA,GAAE,KAAI,KAAA,EAAA,cAAa,MAAK,KAAA,EAAA,iBAAgB,SAAQ,KAAA,EAAA,GAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;MCP9E,QAAK,KAAA,SAAA,SAAA,GAAG,SAAS;MACjB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,WAAQ,KAAA,SAAA,YAAA,GAAG,MAAM;MACjB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,QAAK,KAAA,SAAA,SAAA,GAAG,KAAK;MACpB,iBAAe,cAAQ,EAAC,MAAM,iBAAiB,MAAhC,mBAAoC,OAAM;MACzD,cAAc,SAAQ,EAAC,QAAQ,mBAAmB,EAAE;;;qBAIhD,MAAM,GAAG,CAAC,GAAA,OAAA,CAAAC,WAAK,MAAC;;;;;;uCAIF,KAAI,IAAG,IAAI,GAAE,KAAI,KAAA,EAAA,aAAY,MAAK,KAAA,EAAA,uBAAA,IAAsB,CAAC,KAAA,CACzE,cAAc,GAAE,GAAG,gBAAY,EAAA,GAAA;;mCAFX,MAAK,EAAA,EAAA;;;;;;iDAJK,KAAI,KAAA,EAAA,GAAE,KAAI,KAAA,EAAA,iBAAgB,SAAQ,KAAA,EAAA,GAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;MCVnD,QAAK,KAAA,SAAA,SAAA,GAAG,SAAS;MACzB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,WAAQ,KAAA,SAAA,YAAA,GAAG,IAAI;MACf,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,QAAK,KAAA,SAAA,SAAA,GAAG,KAAK;;;;;;;gCAGa,KAAI,KAAA,EAAA,GAAE,KAAI,KAAA,EAAA,cAAa,MAAK,KAAA,EAAA,iBAAgB,SAAQ,KAAA,EAAA,GAAA;;;iCAC5C,MAAK,EAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;MCR/B,QAAK,KAAA,SAAA,SAAA,GAAG,SAAS;MACzB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,WAAQ,KAAA,SAAA,YAAA,GAAG,OAAO;MAClB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,QAAK,KAAA,SAAA,SAAA,GAAG,KAAK;;;;;;;gCAGa,KAAI,KAAA,EAAA,GAAE,KAAI,KAAA,EAAA,cAAa,MAAK,KAAA,EAAA,iBAAgB,SAAQ,KAAA,EAAA,GAAA;;;iCAC3C,MAAK,EAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MCPxC,QAAK,KAAA,SAAA,SAAA,GAAG,SAAS;MACjB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,WAAQ,KAAA,SAAA,YAAA,GAAG,MAAM;MACjB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,QAAK,KAAA,SAAA,SAAA,GAAG,KAAK;MACpB,iBAAe,cAAQ,EAAC,MAAM,iBAAiB,MAAhC,mBAAoC,OAAM;MACzD,cAAc,SAAQ,EAAC,QAAQ,mBAAmB,EAAE;;;qBAIhD,MAAM,GAAG,CAAC,GAAA,OAAA,CAAAC,WAAK,YAAO;;;;;;iDAIF,OAAO,KAAA,CAAK,cAAc,GAAE,GAAG,gBAAY,EAAA,WAAA,IAAU,OAAO,KAAA,CAClF,KAAI,IAAG,IAAC,CAAI,KAAI,IAAG,MACrB,KAAI,CAAA,GAAA;;mCAHkB,MAAK,EAAA,EAAA;;;;;;kDAJM,KAAI,KAAA,EAAA,GAAE,KAAI,KAAA,EAAA,cAAa,MAAK,KAAA,EAAA,iBAAgB,SAAQ,KAAA,EAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;MCT9E,QAAK,KAAA,SAAA,SAAA,GAAG,SAAS;MACjB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,WAAQ,KAAA,SAAA,YAAA,GAAG,MAAM;MACjB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,QAAK,KAAA,SAAA,SAAA,GAAG,KAAK;MACpB,iBAAe,cAAQ,EAAC,MAAM,iBAAiB,MAAhC,mBAAoC,OAAM;MACzD,cAAc,SAAQ,EAAC,QAAQ,mBAAmB,EAAE;;;qBAShD,MAAM,GAAG,CAAC,GAAA,OAAA,CAAAC,WAAK,YAAO;;;;;;iDAIF,OAAO,KAAA,CAAK,cAAc,GAAE,GAAG,gBAAY,EAAA,YAAA,IAAW,OAAO,KAAA,CACnF,KAAI,IAAG,KACT,KAAI,CAAA,aAAA,IAAa,OAAO,KAAA,CAAK,KAAI,IAAG,KAAM,IAAI,KAAI,CAAA,IAAA;;mCAH5B,MAAK,EAAA,EAAA;;;;;;kDAPd,KAAI,KAAA,EAAA,GAAE,KAAI,KAAA,EAAA,cAAa,MAAK,KAAA,EAAA,kBAAA,CAAkB,KAAI,IACjE,CAAC,GAAE,KAAI,KAAA,EAAA,kBAAA,CAAkB,KAAI,IAAG,CAAC,GAAE,KAAI,KAAA,EAAA,oBAAA,CAAoB,KAAI,IAC/D,CAAC,GAAE,KAAI,KAAA,EAAA,iBAAgB,SAAQ,KAAA,EAAA,GAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;MCbtB,QAAK,KAAA,SAAA,SAAA,GAAG,SAAS;MACjB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,WAAQ,KAAA,SAAA,YAAA,GAAG,IAAI;MACf,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,QAAK,KAAA,SAAA,SAAA,GAAG,KAAK;MACpB,iBAAe,cAAQ,EAAC,MAAM,iBAAiB,MAAhC,mBAAoC,OAAM;MACzD,cAAc,SAAQ,EAAC,QAAQ,mBAAmB,EAAE;;;;;uBAK/C,MAAM,GAAG,CAAC,GAAA,OAAA,CAAAC,WAAK,YAAO;;;;;;+DAIF,OAAO,GAAK,CAAC,IAAA,GAAA,CAC/B,cAAc,CAAC,GAAG,YAAY,KAClC,IAAI,KAAA,cAAA,IAAI,OAAO,GAAK,CAAC,IAAG,eAAe,EAAE,IAAA,cAAA,IAAG,OAAO,GAAK,CAAC,IAAG,eAAe,EAAE,EAAA;;mCAHzD,MAAK,EAAA,EAAA;;;;;;;;;gCALK,KAAI,KAAA,EAAA,GAAE,KAAI,KAAA,EAAA,cAAa,MAAK,KAAA,EAAA,iBAAgB,SAAQ,KAAA,EAAA,GAAA;;;iCAC5C,MAAK,EAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;MCX/B,QAAK,KAAA,SAAA,SAAA,GAAG,SAAS;MACzB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,WAAQ,KAAA,SAAA,YAAA,GAAG,IAAI;MACf,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,QAAK,KAAA,SAAA,SAAA,GAAG,KAAK;;;;;;gCAMP,KAAI,KAAA,EAAA,GAAE,KAAI,KAAA,EAAA,cAAa,MAAK,KAAA,EAAA,iBAAgB,SAAQ,KAAA,EAAA,GAAA;;iCAD7C,MAAK,EAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;MCTV,QAAK,KAAA,SAAA,SAAA,GAAG,SAAS;MACzB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,WAAQ,KAAA,SAAA,YAAA,GAAG,MAAM;MACjB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,QAAK,KAAA,SAAA,SAAA,GAAG,KAAK;;;;;;;gCAGa,KAAI,KAAA,EAAA,GAAE,KAAI,KAAA,EAAA,cAAa,MAAK,KAAA,EAAA,iBAAgB,SAAQ,KAAA,EAAA,GAAA;;;iCAC7C,MAAK,EAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;MCR9B,QAAK,KAAA,SAAA,SAAA,GAAG,SAAS;MACzB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,WAAQ,KAAA,SAAA,YAAA,GAAG,MAAM;MACjB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,QAAK,KAAA,SAAA,SAAA,GAAG,KAAK;MACpB,WAAQ,CAAI,KAAI,IAAG;MACnB,MAAG,CAAI,KAAI,IAAG,IAAI,WAAW;;;;;;;;;;;gCAMhB,KAAI,KAAA,EAAA,GAAE,KAAI,KAAA,EAAA,cAAa,MAAK,KAAA,EAAA,iBAAgB,GAAG,GAAE,KAAI,KAAA,EAAA,iBAAgB,SAAQ,KAAA,EAAA,GAAA;;;;;kCADtE,MAAK,EAAA;kCAGmB,MAAK,EAAA;kCACL,MAAK,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MCd1C,QAAK,KAAA,SAAA,SAAA,GAAG,SAAS;MACjB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,WAAQ,KAAA,SAAA,YAAA,GAAG,MAAM;MACjB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,QAAK,KAAA,SAAA,SAAA,GAAG,KAAK;MACpB,OAAI,eAAA;;;;UACL,MAAO,cAAc,MAAK,GAAE,GAAG,CAAA;;;;;;;;;;;gCAKjB,KAAI,KAAA,EAAA,GAAE,KAAI,KAAA,EAAA,cAAa,MAAK,KAAA,EAAA,aAAA,IAAY,IAAI,KAAA,EAAA,iBAAgB,SAAQ,KAAA,EAAA,GAAA;;;iCAElC,MAAK,EAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MCdrC,QAAK,KAAA,SAAA,SAAA,GAAG,SAAS;MACzB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,WAAQ,KAAA,SAAA,YAAA,GAAG,MAAM;MACjB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,QAAK,KAAA,SAAA,SAAA,GAAG,KAAK;;;;;iCAIP,KAAI,KAAA,EAAA,GAAE,KAAI,KAAA,EAAA,aAAY,MAAK,KAAA,EAAA,iBAAgB,SAAQ,KAAA,EAAA,GAAA;;;iCAC5C,MAAK,EAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;MCTV,QAAK,KAAA,SAAA,SAAA,GAAG,SAAS;MACzB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,WAAQ,KAAA,SAAA,YAAA,GAAG,IAAI;MACf,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,QAAK,KAAA,SAAA,SAAA,GAAG,KAAK;;;;;gCAIP,KAAI,KAAA,EAAA,GAAE,KAAI,KAAA,EAAA,aAAY,MAAK,KAAA,EAAA,gBAAe,SAAQ,KAAA,EAAA,EAAA;;;iCAC3C,MAAK,EAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MCRlB,QAAK,KAAA,SAAA,SAAA,GAAG,SAAS;MACjB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,WAAQ,KAAA,SAAA,YAAA,GAAG,OAAO;MAClB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,QAAK,KAAA,SAAA,SAAA,GAAG,KAAK;MACpB,iBAAe,cAAQ,EAAC,MAAM,iBAAiB,MAAhC,mBAAoC,OAAM;MACzD,cAAc,SAAQ,EAAC,QAAQ,mBAAmB,EAAE;;;qBAIhD,MAAM,IAAI,CAAC,GAAA,OAAA,CAAAC,WAAK,YAAO;;;;;;sCAId,OAAO,KAAA,CAAK,KAAI,IAAG,KAAC,CAAK,KAAI,IAAG,KAAE,CAAI,KAAI,IAAG,QAC1D,KAAI,CAAA,sBAAA,IAAqB,OAAO,KAAA,CAAK,cAAc,IAAG,GAAG,gBAAY,EAAA,GAAA;;mCAF/C,MAAK,EAAA,EAAA;;;;;;kDAJM,KAAI,KAAA,EAAA,GAAE,KAAI,KAAA,EAAA,cAAa,MAAK,KAAA,EAAA,iBAAgB,SAAQ,KAAA,EAAA,GAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;MCT9E,QAAK,KAAA,SAAA,SAAA,GAAG,SAAS;MACjB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,WAAQ,KAAA,SAAA,YAAA,GAAG,IAAI;MACf,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,QAAK,KAAA,SAAA,SAAA,GAAG,KAAK;MACpB,iBAAe,cAAQ,EAAC,MAAM,iBAAiB,MAAhC,mBAAoC,OAAM;MACzD,cAAc,SAAQ,EAAC,QAAQ,mBAAmB,EAAE;MACpD,OAAI,eAAA;;;;UACL,MAAO,cAAc,MAAK,GAAE,CAAC,CAAA;;;;;;sBAOxB,MAAM,GAAG,CAAC,GAAA,OAAA,CAAAC,WAAK,YAAO;;;;;;gEAIF,OAAO,GAAK,CAAC,IAAG,QAAQ,IAAI,yBAAwB,IAAC,CAC5E,cACD,YAAY,GAAA;;mCAHU,MAAK,EAAA,EAAA;;;;;;mDALd,KAAI,KAAA,EAAA,GAAE,KAAI,KAAA,EAAA,cAAa,MAAK,KAAA,EAAA,aAAA,IAAY,IAAI,KAAA,EAAA,iBAAgB,SAAQ,KAAA,EAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;MCdlE,QAAK,KAAA,SAAA,SAAA,GAAG,SAAS;MACzB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,WAAQ,KAAA,SAAA,YAAA,GAAG,MAAM;MACjB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,QAAK,KAAA,SAAA,SAAA,GAAG,KAAK;;;;;;gCAMP,KAAI,KAAA,EAAA,GAAE,KAAI,KAAA,EAAA,cAAa,MAAK,KAAA,EAAA,iBAAgB,SAAQ,KAAA,EAAA,GAAA;;iCAD7C,MAAK,EAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;MCTV,QAAK,KAAA,SAAA,SAAA,GAAG,SAAS;MACzB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,WAAQ,KAAA,SAAA,YAAA,GAAG,MAAM;MACjB,OAAI,KAAA,SAAA,QAAA,GAAG,IAAI;MACX,QAAK,KAAA,SAAA,SAAA,GAAG,KAAK;;;;;;gCAMP,KAAI,KAAA,EAAA,GAAE,KAAI,KAAA,EAAA,cAAa,MAAK,KAAA,EAAA,iBAAgB,SAAQ,KAAA,EAAA,GAAA;;iCAD7C,MAAK,EAAA,EAAA;;;;;;;;;;;;;;;", "names": ["$$anchor", "$$anchor", "$$anchor", "$$anchor", "$$anchor", "$$anchor", "$$anchor", "$$anchor", "$$anchor", "$$anchor", "$$anchor"]}