package esy.app.info;

import esy.api.info.Enum;
import esy.api.info.Land;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.ContextRefreshedEvent;

import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@Tag("fast")
@ExtendWith(MockitoExtension.class)
class LandEnumHandlerTest {

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private EnumRepository enumRepository;

    @Test
    void onApplicationEvent() {
        when(enumRepository.save(any(Enum.class)))
                .thenAnswer(i -> i.getArgument(0));
        when(enumRepository.findByCode(any(String.class), any(Long.class)))
                .thenReturn(Optional.empty());
        final var classUnderTest = new LandEnumHandler(enumRepository);
        classUnderTest.onApplicationEvent(new ContextRefreshedEvent(applicationContext));
        final var times = Land.values().length;
        verify(enumRepository, times(times)).save(any(Enum.class));
        verifyNoMoreInteractions(enumRepository);
        verifyNoInteractions(applicationContext);
    }
}
