package esy.api.plan;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import esy.api.wiki.Seite;
import esy.json.JsonDocument;
import lombok.EqualsAndHashCode;
import lombok.NonNull;

import java.time.LocalDate;
import java.util.LinkedHashSet;
import java.util.Set;
import java.util.UUID;

/**
 * Ein {@link VorgangDto Vorgang} repräsentiert eine Aktivität mit einer Fälligkeit.
 */
@EqualsAndHashCode
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder(alphabetic = true)
@JsonDocument
public class VorgangDto {

    @JsonProperty
    private final VorgangTyp typ;

    @JsonProperty
    private final UUID id;

    @JsonProperty
    private final long version;

    @JsonProperty
    private final String titel;

    @JsonProperty
    private final String text;

    @JsonProperty
    @JsonFormat(pattern = "yyyy-MM-dd")
    private final LocalDate termin;

    @JsonProperty
    private final Terminserie terminserie;

    @JsonProperty
    private final VorgangStatus status;

    @JsonProperty
    private final Set<String> allStichwort = new LinkedHashSet<>();

    public VorgangDto(@NonNull final Aufgabe value) {
        this.typ = VorgangTyp.AUFTRAG;
        this.id = value.getId();
        this.version = value.getVersion();
        this.titel = value.getTitel();
        this.text = value.getText();
        this.termin = value.getTermin();
        this.terminserie = value.getTerminserie();
        this.status = value.getStatus();
        this.allStichwort.addAll(value.getAllStichwort());
    }

    public VorgangDto(@NonNull final Meldung value) {
        this.typ = VorgangTyp.MELDUNG;
        this.id = value.getId();
        this.version = value.getVersion();
        this.titel = value.getTitel();
        this.text = value.getText();
        this.termin = value.getTermin();
        this.terminserie = Terminserie.X;
        this.status = value.isAktiv() ? VorgangStatus.I : VorgangStatus.X;
    }

    public VorgangDto(@NonNull final Risiko value) {
        this.typ = VorgangTyp.RISIKO;
        this.id = value.getId();
        this.version = value.getVersion();
        this.titel = value.getTitel();
        this.text = value.getText();
        this.termin = value.getTermin();
        this.terminserie = Terminserie.X;
        this.status = value.getStatus();
    }

    public VorgangDto(@NonNull final Seite value) {
        this.typ = VorgangTyp.SEITE;
        this.id = value.getId();
        this.version = value.getVersion();
        this.titel = value.getTitel();
        this.text = value.getText();
        this.termin = value.getTermin();
        this.terminserie = Terminserie.X;
        this.status = value.isAktiv() ? VorgangStatus.I : VorgangStatus.X;
    }

    @JsonProperty
    String getPageUri() {
        return String.join("/", this.typ.getBaseUri(), this.id.toString());
    }

    @JsonProperty
    boolean isAktiv() {
        return !VorgangStatus.X.equals(this.status);
    }
}
