package esy.app;

import com.microsoft.playwright.APIRequest;
import com.microsoft.playwright.APIRequestContext;
import com.microsoft.playwright.APIResponse;
import com.microsoft.playwright.Playwright;
import com.microsoft.playwright.options.RequestOptions;
import esy.api.plan.Projekt;
import esy.api.plan.Risiko;
import esy.api.team.Nutzer;
import esy.auth.JwtRole;
import esy.json.JsonMapper;
import lombok.NonNull;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.client.RestClientResponseException;

import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.*;
import java.util.function.Function;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.jupiter.api.Assertions.assertNotNull;

public abstract class PlaywrightApiAssertionBase implements AutoCloseable {

    private final PlaywrightApiTestEnv env;

    private final APIRequestContext context;

    private final List<String> allAutoDelete = new ArrayList<>();

    protected PlaywrightApiAssertionBase(@NonNull final Playwright playwright, @NonNull final PlaywrightApiTestEnv env) {
        this.env = env;
        this.context = playwright.request().newContext(new APIRequest.NewContextOptions()
                .setBaseURL(env.getBaseUrl())
                .setTimeout(Duration.ofMinutes(5L).toMillis())
                .setExtraHTTPHeaders(Map.of(
                        HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE
                )));
    }

    @Override
    public void close() {
        allAutoDelete.forEach((path) -> doWithApi(
                (api) -> api.delete(path),
                APIResponse::ok));
        allAutoDelete.clear();
    }

    protected final void login() {
        final var res = context.post("/jwt", RequestOptions.create()
                .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE)
                .setHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
                .setQueryParam("email", env.getEmail())
                .setQueryParam("login", env.getLogin())
                .setQueryParam("title", env.getTitle())
                .setQueryParam("allRole", String.join(",",
                        JwtRole.ANWENDUNG,
                        JwtRole.VERWALTUNG)));
        if (!res.ok()) {
            throw new RestClientResponseException(
                    String.format("%s not logged in", env.getEmail()),
                    res.status(),
                    res.statusText(),
                    null,
                    res.body(),
                    StandardCharsets.UTF_8);
        }
    }

    protected final <T> T doWithApi(@NonNull final Function<APIRequestContext, APIResponse> operation, @NonNull final Function<APIResponse, T> converter) {
        return converter.apply(operation.apply(context));
    }

    protected final List<String> fetchAllGruppeId() {
        return doWithApi(
                (api) -> api.get("/api/gruppe"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    return new JsonMapper().parseJsonPath(res.text()).readContentId();
                });
    }

    protected final List<String> fetchAllNutzerId() {
        return doWithApi(
                (api) -> api.get("/api/nutzer"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    return new JsonMapper().parseJsonPath(res.text()).readContentId();
                });
    }

    protected final Nutzer createRandomNutzer() {
        final var name = randomName();
        return doWithApi(
                (api) -> api.post("/api/nutzer", RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"mail\":\"%s\",\"name\":\"%s\",\"aktiv\":\"true\"}",
                                String.join("@", name,  "a.de"),
                                String.join(" ", "Dirk", name)))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.CREATED.value()));
                    final var json = Nutzer.parseJson(res.text());
                    allAutoDelete.add("/api/nutzer/" + json.getId());
                    assertNotNull(json.getId());
                    assertNotNull(json.getName());
                    assertNotNull(json.getMail());
                    return json;
                });
    }

    protected final Projekt createRandomProjekt() {
        final var name = randomName();
        return doWithApi(
                (api) -> api.post("/api/projekt", RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"name\":\"%s\",\"aktiv\":\"true\"}",
                                name))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.CREATED.value()));
                    final var json = Projekt.parseJson(res.text());
                    allAutoDelete.add("/api/projekt/" + json.getId());
                    assertNotNull(json.getId());
                    assertNotNull(json.getName());
                    return json;
                });
    }

    protected final Risiko createRandomRisiko() {
        final var titel = randomName();
        final var text = loremIpsum();
        return doWithApi(
                (api) -> api.post("/api/risiko", RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"titel\":\"%s\",\"text\":\"%s\"}",
                                titel, text))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.CREATED.value()));
                    final var json = Risiko.parseJson(res.text());
                    allAutoDelete.add("/api/projekt/" + json.getId());
                    assertNotNull(json.getId());
                    assertNotNull(json.getTitel());
                    assertNotNull(json.getText());
                    return json;
                });
    }

    protected static String randomName() {
        return "X" + UUID.randomUUID().toString()
                .replace("-", "")
                .replaceAll("\\d", "");
    }

    @SuppressWarnings({"java:S2245", "java:S2119"}) // safe, no reuse
    protected static int randomIndex(int limit) {
        return new Random().nextInt(limit);
    }

    protected static final List<String> allLoremIpsum = List.of(
            "Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua.",
            "At vero eos et accusam et justo duo dolores et ea rebum.",
            "Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.",
            "Magni accusantium labore et id quis provident.",
            "Consectetur impedit quisquam qui deserunt non rerum consequuntur eius.",
            "Quia atque aliquam sunt impedit voluptatum rerum assumenda nisi.",
            "Cupiditate quos possimus corporis quisquam exercitationem beatae."
    );

    protected static String loremIpsum(int index) {
        return allLoremIpsum.get(index % allLoremIpsum.size());
    }

    protected static String loremIpsum() {
        return loremIpsum(randomIndex(allLoremIpsum.size()));
    }
}
