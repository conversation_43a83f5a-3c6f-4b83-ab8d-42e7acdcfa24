package esy;

import com.microsoft.playwright.Playwright;
import esy.app.PlaywrightApiTestEnv;
import esy.app.info.EnumPlaywrightApiAssertion;
import esy.app.info.PingPlaywrightApiAssertion;
import esy.app.ort.AdressePlaywrightApiAssertion;
import esy.app.plan.AufgabePlaywrightApiAssertion;
import esy.app.plan.ProjektPlaywrightApiAssertion;
import esy.app.plan.RisikoPlaywrightApiAssertion;
import esy.app.team.GruppePlaywrightApiAssertion;
import esy.app.team.NutzerPlaywrightApiAssertion;
import esy.app.zeit.AbwesenheitPlaywrightApiAssertion;
import esy.app.zeit.AnwesenheitPlaywrightApiAssertion;
import esy.app.zeit.EinsatzPlaywrightApiAssertion;
import esy.auth.JwtUser;
import esy.json.JsonMapper;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.DisabledIfEnvironmentVariable;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.jdbc.EmbeddedDatabaseConnection;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.testcontainers.service.connection.ServiceConnection;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpStatus;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.containers.output.Slf4jLogConsumer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import javax.sql.DataSource;
import java.util.Properties;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.*;
import static org.junit.jupiter.api.Assertions.*;

@Tag("slow")
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(connection = EmbeddedDatabaseConnection.NONE)
@Testcontainers
@ExtendWith(MockitoExtension.class)
@DisabledIfEnvironmentVariable(named = "CI", matches = ".*")
@SuppressWarnings("java:S5961") // allow more than 25 assertions
class EsyBackendRunnerTest {

    static {
        // turn off oauth2 login
        System.setProperty("oauth2.login", "");
    }

    static final Logger LOG = LoggerFactory.getLogger(EsyBackendRunnerTest.class);

    @Container
    @ServiceConnection
    static final PostgreSQLContainer<?> DATABASE = new PostgreSQLContainer<>("postgres:17")
            .withLogConsumer(new Slf4jLogConsumer(LOG));

    @Value(value = "${local.server.port}")
    private int backendPort;

    @Value("${backend.email:" + JwtUser.DEFAULT_EMAIL + "}")
    private String backendEmail;

    @Value("${backend.login:" + JwtUser.DEFAULT_LOGIN + "}")
    private String backendLogin;

    @Value("${backend.title:" + JwtUser.DEFAULT_TITLE + "}")
    private String backendTitle;

    @Autowired
    private DataSource dataSource;

    @Test
    void databaseProperties() throws Exception {
        try (final var connection = dataSource.getConnection()) {
            final var metadata = connection.getMetaData();
            assertEquals(DATABASE.getJdbcUrl(), metadata.getURL());
            assertEquals("PostgreSQL", metadata.getDatabaseProductName());
            assertEquals(17, metadata.getDatabaseMajorVersion());
        }
    }

    @Test
    void applicationProperties() throws Exception {
        final var fileUnderTest = "application.properties";
        final var fileResource = new ClassPathResource(fileUnderTest);
        assertTrue(fileResource.exists(), fileUnderTest);
        final var env = new Properties();
        try (final var is = fileResource.getInputStream()) {
            env.load(is);
            assertEquals(11, env.size());
            // Kein Banner wegen JSON-Logging
            assertThat(env.getProperty("spring.main.banner-mode"),
                    is("off"));
            // org.springframework.boot.autoconfigure.jdbc.DatasourceProperties
            assertThat(env.getProperty("spring.datasource.url"),
                    containsString("jdbc:postgresql"));
            assertThat(env.getProperty("spring.datasource.username"),
                    not(emptyString()));
            assertThat(env.getProperty("spring.datasource.password"),
                    not(emptyString()));
            // org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties
            assertThat(env.getProperty("spring.jpa.properties.hibernate.show_sql"),
                    is("false"));
            assertThat(env.getProperty("spring.jpa.properties.hibernate.format_sql"),
                    is("false"));
            assertThat(env.getProperty("spring.jpa.properties.hibernate.generate_statistics"),
                    is("false"));
            // DEBUG für erweitertes Logging für Web-Operationen
            assertThat(env.getProperty("logging.level.web"),
                    is("INFO"));
            // DEBUG für erweitertes Logging von SQL-Statements
            assertThat(env.getProperty("logging.level.org.hibernate.SQL"),
                    is("INFO"));
            // TRACE für erweitertes Logging von Werten in SQL-Statements
            assertThat(env.getProperty("logging.level.org.hibernate.orm.jdbc.bind"),
                    is("INFO"));
            // DEBUG für erweitertes Logging von SQL-Statistiken
            assertThat(env.getProperty("logging.level.org.hibernate.stat"),
                    is("INFO"));
        }
    }

    String toBackendUrl(final String path) {
        return "http://localhost:" + backendPort + path;
    }

    @Test
    void healthApi() {
        try (final var playwright = Playwright.create()) {
            final var req = playwright.request().newContext();
            final var res = req.get(toBackendUrl("/actuator/health"));
            assertThat(res.status(), equalTo(HttpStatus.OK.value()));
            final var jsonReader = new JsonMapper().parseJsonPath(res.text());
            assertEquals("UP", jsonReader.read("$.status"));
            assertEquals("liveness", jsonReader.read("$.groups[0]"));
            assertEquals("readiness", jsonReader.read("$.groups[1]"));
        }
    }

    @Test
    void versionApi() {
        try (final var playwright = Playwright.create()) {
            final var req = playwright.request().newContext();
            final var res = req.get(toBackendUrl("/version"));
            assertThat(res.status(), equalTo(HttpStatus.OK.value()));
            final var jsonReader = new JsonMapper().parseJsonPath(res.text());
            assertNotNull(jsonReader.read("$.major"));
            assertNotNull(jsonReader.read("$.minor"));
            assertNotNull(jsonReader.read("$.version"));
            assertNotNull(jsonReader.read("$.oauth2"));
        }
    }

    PlaywrightApiTestEnv buildTestEnv() {
        return PlaywrightApiTestEnv.builder()
                .baseUrl(toBackendUrl(""))
                .email(backendEmail)
                .login(backendLogin)
                .title(backendTitle)
                .build();
    }

    @Test
    void enumApi() {
        try (final var playwright = Playwright.create()) {
            try (final var assertion = new EnumPlaywrightApiAssertion(playwright, buildTestEnv())) {
                assertion.assertEnumKanal();
                assertion.assertEnumQuelle();
                assertion.assertEnumSprache();
                assertion.assertEnumHistory();
            }
        }
    }

    @ParameterizedTest
    @ValueSource(strings = {
            "Ping mit Leerzeichen.",
            "ping mit 'Äpfel'",
            "ping mit 'Öl'",
            "ping mit 'Übel'",
            "ping mit 'Spaß'",
            "Ping mit $ond€rze!c#en."
    })
    void pingApi(final String text) {
        try (final var playwright = Playwright.create()) {
            try (final var assertion = new PingPlaywrightApiAssertion(playwright, buildTestEnv())) {
                assertion.assertPing(text);
            }
        }
    }

    @Test
    void gruppeApi() {
        try (final var playwright = Playwright.create()) {
            try (final var assertion = new GruppePlaywrightApiAssertion(playwright, buildTestEnv())) {
                assertion.assertGruppe();
            }
        }
    }

    @Test
    void nutzerApi() {
        try (final var playwright = Playwright.create()) {
            try (final var assertion = new NutzerPlaywrightApiAssertion(playwright, buildTestEnv())) {
                assertion.assertNutzer();
                assertion.assertNutzerKanal();
                assertion.assertNutzerSprache();
                assertion.assertNutzerGruppe();
            }
        }
    }

    @Test
    void projektApi() {
        try (final var playwright = Playwright.create()) {
            try (final var assertion = new ProjektPlaywrightApiAssertion(playwright, buildTestEnv())) {
                assertion.assertProjekt();
                assertion.assertProjektOption();
                assertion.assertProjektQuelle();
                assertion.assertProjektBesitzer();
                assertion.assertProjektMitglied();
            }
        }
    }

    @Test
    void aufgabeApi() {
        try (final var playwright = Playwright.create()) {
            try (final var assertion = new AufgabePlaywrightApiAssertion(playwright, buildTestEnv())) {
                assertion.assertAufgabe();
                assertion.assertAufgabeNutzer();
            }
        }
    }

    @Test
    void abwesenheitApi() {
        try (final var playwright = Playwright.create()) {
            try (final var assertion = new AbwesenheitPlaywrightApiAssertion(playwright, buildTestEnv())) {
                assertion.assertAbwesenheit();
                assertion.assertAbwesenheitBesitzer();
            }
        }
    }

    @Test
    void anwesenheitApi() {
        try (final var playwright = Playwright.create()) {
            try (final var assertion = new AnwesenheitPlaywrightApiAssertion(playwright, buildTestEnv())) {
                assertion.assertAnwesenheit();
                assertion.assertAnwesenheitBesitzer();
            }
        }
    }

    @Test
    void einsatzApi() {
        try (final var playwright = Playwright.create()) {
            try (final var assertion = new EinsatzPlaywrightApiAssertion(playwright, buildTestEnv())) {
                assertion.assertEinsatz();
                assertion.assertEinsatzBesitzer();
                assertion.assertEinsatzAnwesenheit();
                assertion.assertEinsatzAbwesenheit();
            }
        }
    }

    @Test
    void adresseApi() {
        try (final var playwright = Playwright.create()) {
            try (final var assertion = new AdressePlaywrightApiAssertion(playwright, buildTestEnv())) {
                assertion.assertAdresse();
            }
        }
    }

    @Test
    void risikoApi() {
        try (final var playwright = Playwright.create()) {
            try (final var assertion = new RisikoPlaywrightApiAssertion(playwright, buildTestEnv())) {
                assertion.assertRisiko();
                assertion.assertRisikoNutzer();
            }
        }
    }
}
