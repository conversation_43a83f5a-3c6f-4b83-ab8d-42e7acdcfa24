package esy.app.team;

import com.microsoft.playwright.Playwright;
import com.microsoft.playwright.options.RequestOptions;
import esy.api.team.Gruppe;
import esy.app.PlaywrightApiAssertionBase;
import esy.app.PlaywrightApiTestEnv;
import esy.json.JsonMapper;
import lombok.NonNull;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;

import java.util.UUID;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.jupiter.api.Assertions.*;

@SuppressWarnings("java:S1192") // duplicating literals is ok
public class GruppePlaywrightApiAssertion extends PlaywrightApiAssertionBase {

    public GruppePlaywrightApiAssertion(@NonNull final Playwright playwright, @NonNull final PlaywrightApiTestEnv env) {
        super(playwright, env);
        login();
    }

    public void assertGruppe() {
        final var gruppeName1 = "A-Team";
        final var gruppeId1 = doWithApi(
                (api) -> api.post("/api/gruppe", RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"name\":\"%s\",\"aktiv\":\"false\",\"verwaltung\":\"true\"}", gruppeName1))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.CREATED.value()));
                    final var json = Gruppe.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(gruppeName1, json.getName());
                    assertFalse(json.isAktiv());
                    assertTrue(json.isVerwaltung());
                    return json.getId();
                });

        final var gruppeName2 = "B-Team";
        final var gruppeId2 = doWithApi(
                (api) -> api.put("/api/gruppe/" + UUID.randomUUID(), RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"name\":\"%s\",\"aktiv\":\"false\",\"verwaltung\":\"false\"}", gruppeName2))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.CREATED.value()));
                    final var json = Gruppe.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertNotEquals(gruppeId1, json.getId());
                    assertEquals(gruppeName2, json.getName());
                    assertFalse(json.isAktiv());
                    assertFalse(json.isVerwaltung());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.put("/api/gruppe/" + gruppeId2, RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"name\":\"%s\",\"aktiv\":\"true\",\"verwaltung\":\"false\"}", gruppeName2))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Gruppe.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(gruppeName2, json.getName());
                    assertTrue(json.isAktiv());
                    assertFalse(json.isVerwaltung());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.patch("/api/gruppe/" + gruppeId2, RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, "application/merge-patch+json")
                        .setData("{\"verwaltung\":\"true\"}")),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Gruppe.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(gruppeName2, json.getName());
                    assertTrue(json.isAktiv());
                    assertTrue(json.isVerwaltung());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.get("/api/gruppe"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var jsonReader = new JsonMapper().parseJsonPath(res.text());
                    assertNotNull(jsonReader.read("$.page"));
                    final var allGruppeName = jsonReader.readContentField("name");
                    assertTrue(allGruppeName.contains(gruppeName1));
                    assertTrue(allGruppeName.contains(gruppeName2));
                    final var allGruppeId = jsonReader.readContentId();
                    assertTrue(allGruppeId.contains(gruppeId1.toString()));
                    assertTrue(allGruppeId.contains(gruppeId2.toString()));
                    return allGruppeId;
                });

        doWithApi(
                (api) -> api.get("/api/gruppe/" + gruppeId1),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Gruppe.parseJson(res.text());
                    assertEquals(gruppeId1, json.getId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.get("/api/gruppe/search/findByName", RequestOptions.create()
                        .setQueryParam("name", gruppeName1)),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Gruppe.parseJson(res.text());
                    assertEquals(gruppeId1, json.getId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.delete("/api/gruppe/" + gruppeId1),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Gruppe.parseJson(res.text());
                    assertEquals(gruppeId1, json.getId());
                    assertEquals(gruppeName1, json.getName());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.delete("/api/gruppe/" + gruppeId2),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Gruppe.parseJson(res.text());
                    assertEquals(gruppeId2, json.getId());
                    assertEquals(gruppeName2, json.getName());
                    return json.getId();
                });
    }
}
