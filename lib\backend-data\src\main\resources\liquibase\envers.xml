<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.9.xsd">
    <changeSet id="1" author="robert">
        <preConditions onFail="MARK_RAN">
            <not><tableExists tableName="revinfo" /></not>
        </preConditions>
        <createSequence sequenceName="revinfo_seq"
                        incrementBy="1"
                        startValue="1"
                        cycle="true"
        />
        <createTable tableName="revinfo">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="timestamp" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>        
</databaseChangeLog>
