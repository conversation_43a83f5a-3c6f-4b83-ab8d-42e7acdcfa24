plugins {
    id 'eclipse'
    id 'jacoco'
    id 'java'
    id 'io.spring.dependency-management'
    id 'org.springframework.boot'
    id 'com.google.cloud.tools.jib'
    id 'com.diffplug.spotless'
}

dependencies {
    implementation project(':lib:backend-data')
    implementation project(':lib:openapi-github')
    implementation project(':lib:openapi-jira')
    // https://projectlombok.org
    implementation('org.projectlombok:lombok')
    annotationProcessor('org.projectlombok:lombok')
    // https://spring.io/projects/spring-boot
	implementation('org.springframework.boot:spring-boot-starter-log4j2')
    // https://spring.io/projects/spring-data
    runtimeOnly('org.hsqldb:hsqldb')
    runtimeOnly('org.postgresql:postgresql')
}
dependencies {
    testImplementation project(':lib:backend-test')
    // https://spring.io/projects/spring-boot
    testImplementation('org.springframework.boot:spring-boot-starter-test')
    testImplementation('org.springframework.boot:spring-boot-testcontainers')
    // https://www.testcontainers.org
    testImplementation('org.testcontainers:junit-jupiter')
    testImplementation('org.testcontainers:postgresql')
}

configurations {
	configureEach {
		exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
	}
}

springBoot {
    mainClass = 'esy.EsyBackendRunner'
    buildInfo {
		properties {
            additional = [
				'root': rootProject.name
			]
		}
	}
}

apply from: "${rootDir}/gradle/boot-build.gradle"
apply from: "${rootDir}/gradle/java-test.gradle"
