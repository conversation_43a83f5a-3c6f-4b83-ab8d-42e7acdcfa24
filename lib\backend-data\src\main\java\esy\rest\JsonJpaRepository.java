package esy.rest;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.repository.NoRepositoryBean;
import org.springframework.data.repository.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Collection resources support both GET and POST.
 * The following methods are used if present:
 * <ul>
 *     <li>findAll(Pageable)</li>
 *     <li>findAll(Sort)</li>
 *     <li>findAll()</li>
 *     <li>save(…)</li>
 * </ul>
 *
 * Item resources support GET, PUT, PATCH and DELETE.
 * The following methods are used if present:
 * <ul>
 *     <li>findById(…)</li>
 *     <li>save(…)</li>
 *     <li>delete(…)</li>
 * </ul>
 *
 * @see <a href="https://docs.spring.io/spring-data/rest/reference/repository-resources.html">Repository resources</a>
 * @see <a href="https://docs.spring.io/spring-data/rest/reference/data-commons/repositories/definition.html">Defining Repository Interfaces</a>
 */
@NoRepositoryBean
@SuppressWarnings("java:S119") // ID is ok
public interface JsonJpaRepository<T, ID> extends Repository<T, ID> {

    /**
     * @see org.springframework.data.repository.CrudRepository#save(Object) 
     */
    <S extends T> S save(S entity);

    /**
     * @see org.springframework.data.repository.CrudRepository#delete(Object) 
     */
    void delete(T entity);

    /**
     * @see org.springframework.data.repository.CrudRepository#count() 
     */
    long count();

    /**
     * @see org.springframework.data.jpa.repository.JpaRepository#getReferenceById(Object)
     */
    T getReferenceById(ID id);

    /**
     * @see org.springframework.data.repository.CrudRepository#findById(Object)
     */
    Optional<T> findById(ID id);

    /**
     * @see org.springframework.data.repository.CrudRepository#existsById(Object) 
     */
    boolean existsById(ID id);

    /**
     * @see org.springframework.data.repository.ListCrudRepository#findAll()
     */
    List<T> findAll();

    /**
     * @see org.springframework.data.repository.ListPagingAndSortingRepository#findAll(Sort) 
     */
    List<T> findAll(Sort sort);

    /**
     * @see org.springframework.data.repository.PagingAndSortingRepository#findAll(Pageable) 
     */
    Page<T> findAll(Pageable pageable);
}
