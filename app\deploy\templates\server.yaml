apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: {{.Release.Namespace}}
  name: server-deployment
spec:
  replicas: 1
  selector:
    matchLabels:
      component: server
  template:
    metadata:
      labels:
        release: {{.Release.Name}}
        version: {{.Chart.Version}}
        chart: {{.Chart.Name}}
        component: server
    spec:
{{ if .Values.image.secret }}
      imagePullSecrets:
        - name: {{ .Values.image.secret }}
{{ end }}
      volumes:
        - name: backend-container-volume
          emptyDir: {}
      initContainers:
        - name: backend-container-keytool
          # tag::java[]
          image: eclipse-temurin:21-jdk-alpine@sha256:cafcfad1d9d3b6e7dd983fa367f085ca1c846ce792da59bcb420ac4424296d56
          # end::java[]
          volumeMounts:
            - name: backend-container-volume
              mountPath: /mnt
          env:
            - name: KEYSTORE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{.Values.application.name}}-keytool
                  key: password
            - name: KEYSTORE_FILENAME
              valueFrom:
                secretKeyRef:
                  name: {{.Values.application.name}}-keytool
                  key: filename
          command:
            - "sh"
            - "-c"
            - |
              rm -f /mnt/${KEYSTORE_FILENAME}
              keytool \
                -genkey \
                -noprompt \
                -alias scs \
                -validity 365 \
                -keystore /mnt/${KEYSTORE_FILENAME} \
                -storepass ${KEYSTORE_PASSWORD} \
                -keypass ${KEYSTORE_PASSWORD} \
                -keyalg RSA \
                -keysize 2048 \
                -dname "CN={{.Release.Name}}, C=AT"
      containers:
        - name: backend-container
          image: "{{.Values.image.repository}}/{{.Values.image.backendImageName}}:{{.Values.image.tag}}"
          imagePullPolicy: IfNotPresent
          volumeMounts:
            - name: backend-container-volume
              mountPath: /app/resources
          # tag::env[]
          env:
            - name: _JAVA_OPTIONS
              value: "-Xdebug -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005"
            - name: SERVER_PORT
              value: "{{.Values.image.backendPort}}"
            - name: SERVER_TOMCAT_THREADS_MAX
              value: "50"
            - name: SPRING_APPLICATION_NAME
              value: "{{.Values.image.backendImageName}}"
            - name: SPRING_PROFILES_ACTIVE
              value: "k8s"
            - name: SPRING_LIQUIBASE_DEFAULTSCHEMA
              value: "{{.Values.database.schema}}"
            - name: SPRING_DATASOURCE_URL
              value: "jdbc:postgresql://postgres:{{.Values.database.port}}/{{.Values.database.name}}?currentSchema={{.Values.database.schema}}"
            - name: SPRING_DATASOURCE_USERNAME
              valueFrom:
                secretKeyRef:
                  name: {{.Values.application.name}}-db
                  key: username
            - name: SPRING_DATASOURCE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{.Values.application.name}}-db
                  key: password
            - name: KEYSTORE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{.Values.application.name}}-keytool
                  key: password
            - name: KEYSTORE_FILENAME
              valueFrom:
                secretKeyRef:
                  name: {{.Values.application.name}}-keytool
                  key: filename
            - name: OAUTH2_LOGIN
              value: {{.Values.application.login}}
            {{ if eq .Values.application.login "" }}
            - name: OAUTH2_CLIENTID
              value: ""
            - name: OAUTH2_CLIENTSECRET
              value: ""
            {{ else if eq .Values.application.login "github" }}
            - name: OAUTH2_CLIENTID
              valueFrom:
                secretKeyRef:
                  name: {{.Values.application.name}}-github
                  key: clientId
            - name: OAUTH2_CLIENTSECRET
              valueFrom:
                secretKeyRef:
                  name: {{.Values.application.name}}-github
                  key: clientSecret
            {{ else if eq .Values.application.login "keycloak" }}
            - name: OAUTH2_ISSUERURI
              value: "{{.Values.keycloak.baseUri}}/realms/{{.Values.keycloak.realm}}"
            - name: OAUTH2_CLIENTID
              valueFrom:
                secretKeyRef:
                  optional: true
                  name: {{.Values.application.name}}-keycloak
                  key: clientId
            - name: OAUTH2_CLIENTSECRET
              valueFrom:
                secretKeyRef:
                  optional: true
                  name: {{.Values.application.name}}-keycloak
                  key: clientSecret
            {{ end }}
            - name: OAUTH2_EXPIRE
              value: "43200"
            - name: GITHUB_API_URL
              valueFrom:
                secretKeyRef:
                  optional: true
                  name: {{.Values.application.name}}-github
                  key: apiUrl
            - name: GITHUB_TOKEN
              valueFrom:
                secretKeyRef:
                  optional: true
                  name: {{.Values.application.name}}-github
                  key: token
            - name: GITLAB_TOKEN
              valueFrom:
                secretKeyRef:
                  optional: true
                  name: {{.Values.application.name}}-gitlab
                  key: token
            - name: JIRA_API_URL
              valueFrom:
                secretKeyRef:
                  optional: true
                  name: {{.Values.application.name}}-jira
                  key: apiUrl
            - name: JIRA_TOKEN
              valueFrom:
                secretKeyRef:
                  optional: true
                  name: {{.Values.application.name}}-jira
                  key: token
          # end::env[]
          # tag::ports[]
          ports:
            - containerPort: {{.Values.image.backendPort}}
              name: backend-http
          # end::ports[]
          # tag::resources[]
          resources:
            requests:
              cpu: {{.Values.image.backendCpu}}
              memory: {{.Values.image.backendMemory}}
            limits:
              #cpu: no limit on single node
              memory: {{.Values.image.backendMemory}}
          # end::resources[]
          # tag::readiness[]
          readinessProbe:
            initialDelaySeconds: 90
            periodSeconds: 3
            failureThreshold: 60
            timeoutSeconds: 1
            httpGet:
              port: backend-http
              path: /actuator/health/readiness
          # end::readiness[]
          # tag::liveness[]
          livenessProbe:
            initialDelaySeconds: 300
            periodSeconds: 30
            failureThreshold: 60
            timeoutSeconds: 1
            httpGet:
              port: backend-http
              path: /actuator/health/liveness
          # tag::liveness[]
        - name: printer-container
          image: "{{.Values.image.repository}}/{{.Values.image.printerImageName}}:{{.Values.image.tag}}"
          imagePullPolicy: IfNotPresent
          # tag::env[]
          env:
            - name: _JAVA_OPTIONS
              value: "-Xdebug -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5006"
            - name: SERVER_PORT
              value: "{{.Values.image.printerPort}}"
            - name: SERVER_TOMCAT_THREADS_MAX
              value: "50"
            - name: BACKEND_URL
              value: "http://localhost:{{.Values.image.backendPort}}"
            - name: SPRING_APPLICATION_NAME
              value: "{{.Values.image.printerImageName}}"
            - name: SPRING_PROFILES_ACTIVE
              value: "k8s"
            - name: GITHUB_API_URL
              valueFrom:
                secretKeyRef:
                  optional: true
                  name: {{.Values.application.name}}-github
                  key: apiUrl
            - name: GITHUB_TOKEN
              valueFrom:
                secretKeyRef:
                  optional: true
                  name: {{.Values.application.name}}-github
                  key: token
            - name: GITLAB_TOKEN
              valueFrom:
                secretKeyRef:
                  optional: true
                  name: {{.Values.application.name}}-gitlab
                  key: token
          # end::env[]
          # tag::ports[]
          ports:
            - containerPort: {{.Values.image.printerPort}}
              name: printer-http
          # end::ports[]
          # tag::resources[]
          resources:
            requests:
              cpu: {{.Values.image.printerCpu}}
              memory: {{.Values.image.printerMemory}}
            limits:
              #cpu: no limit on single node
              memory: {{.Values.image.printerMemory}}
          # end::resources[]
          # tag::readiness[]
          readinessProbe:
            initialDelaySeconds: 90
            periodSeconds: 3
            failureThreshold: 60
            timeoutSeconds: 1
            httpGet:
              port: printer-http
              path: /actuator/health/readiness
          # end::readiness[]
          # tag::liveness[]
          livenessProbe:
            initialDelaySeconds: 300
            periodSeconds: 30
            failureThreshold: 60
            timeoutSeconds: 1
            httpGet:
              port: printer-http
              path: /actuator/health/liveness
          # end::liveness[]
        - name: spooler-container
          image: "{{.Values.image.repository}}/{{.Values.image.spoolerImageName}}:{{.Values.image.tag}}"
          imagePullPolicy: IfNotPresent
          # tag::env[]
          env:
            - name: SERVER_PORT
              value: "{{.Values.image.spoolerPort}}"
            - name: BACKEND_URL
              value: "http://localhost:{{.Values.image.backendPort}}"
            - name: PRINTER_URL
              value: "http://localhost:{{.Values.image.printerPort}}"
            - name: CUPS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{.Values.application.name}}-cups
                  key: password
            - name: SMTP_HOST
              valueFrom:
                secretKeyRef:
                  name: {{.Values.application.name}}-smtp
                  key: host
            - name: SMTP_PORT
              valueFrom:
                secretKeyRef:
                  name: {{.Values.application.name}}-smtp
                  key: port
            - name: SMTP_USERNAME
              valueFrom:
                secretKeyRef:
                  name: {{.Values.application.name}}-smtp
                  key: username
            - name: SMTP_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{.Values.application.name}}-smtp
                  key: password
          # end::env[]
          # tag::ports[]
          ports:
            - containerPort: {{.Values.image.spoolerPort}}
              name: spooler-http
            - containerPort: 631
              name: spooler-cups
          # end::ports[]
          # tag::resources[]
          resources:
            requests:
              cpu: {{.Values.image.spoolerCpu}}
              memory: {{.Values.image.spoolerMemory}}
            limits:
              #cpu: no limit on single node
              memory: {{.Values.image.spoolerMemory}}
          # end::resources[]
          # tag::readiness[]
          readinessProbe:
            initialDelaySeconds: 5
            periodSeconds: 3
            failureThreshold: 10
            timeoutSeconds: 1
            httpGet:
              port: spooler-http
              path: /healthz
          # end::readiness[]
          # tag::liveness[]
          # No values
          # end::liveness[]
---
apiVersion: v1
kind: Service
metadata:
  namespace: {{.Release.Namespace}}
  name: server-cluster-ip
spec:
  type: ClusterIP
  selector:
    component: server
  ports:
    - port: {{.Values.image.backendPort}}
      name: backend-http
      targetPort: backend-http
    - port: {{.Values.image.printerPort}}
      name: printer-http
      targetPort: printer-http
    - port: {{.Values.image.spoolerPort}}
      name: spooler-http
      targetPort: spooler-http
    - port: 631
      name: spooler-cups
      targetPort: spooler-cups
