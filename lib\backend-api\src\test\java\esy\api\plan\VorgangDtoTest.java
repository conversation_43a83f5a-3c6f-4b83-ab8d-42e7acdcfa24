package esy.api.plan;

import esy.api.wiki.Seite;
import esy.json.JsonMapper;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.*;

class VorgangDtoTest {

    @Test
    void applyAufgabe() {
        final var value = Aufgabe.parseJson("{" +
                "\"titel\":\"Lorem ipsum\"," +
                "\"text\":\"Lorem ipsum dolor sit amet.\"," +
                "\"termin\":\"2021-04-22\"" +
                "}");
        final var dto = new VorgangDto(value);
        assertEquals(String.format("/aufgabe/%s", value.getId()), dto.getPageUri());
        assertTrue(dto.isAktiv());
        final var json = new JsonMapper().writeJson(dto);
        final var jsonReader = new JsonMapper().parseJsonPath(json);
        assertNotNull(jsonReader.read("id"));
        assertEquals(0, jsonReader.read("version", Integer.class));
        assertEquals(true, jsonReader.read("aktiv", Boolean.class));
        assertEquals(value.getTitel(), jsonReader.read("titel", String.class));
        assertEquals(value.getText(), jsonReader.read("text", String.class));
        assertEquals(value.getTermin(), jsonReader.read("termin", LocalDate.class));
        assertEquals(value.getTerminserie(), jsonReader.read("terminserie", Terminserie.class));
        assertEquals(value.getStatus(), jsonReader.read("status", VorgangStatus.class));
        assertNotNull(jsonReader.read("allStichwort"));
    }

    @Test
    void applyMeldung() {
        final var value = Meldung.parseJson("{" +
                "\"titel\":\"Lorem ipsum\"," +
                "\"text\":\"Lorem ipsum dolor sit amet.\"," +
                "\"aktiv\":\"true\"," +
                "\"termin\":\"2021-04-22\"" +
                "}");
        final var dto = new VorgangDto(value);
        assertEquals(String.format("/meldung/%s", value.getId()), dto.getPageUri());
        assertTrue(dto.isAktiv());
        final var json = new JsonMapper().writeJson(dto);
        final var jsonReader = new JsonMapper().parseJsonPath(json);
        assertNotNull(jsonReader.read("id"));
        assertEquals(0, jsonReader.read("version", Integer.class));
        assertEquals(true, jsonReader.read("aktiv", Boolean.class));
        assertEquals(value.getTitel(), jsonReader.read("titel", String.class));
        assertEquals(value.getText(), jsonReader.read("text", String.class));
        assertEquals(value.getTermin(), jsonReader.read("termin", LocalDate.class));
        assertEquals(Terminserie.X, jsonReader.read("terminserie", Terminserie.class));
        assertEquals(VorgangStatus.I, jsonReader.read("status", VorgangStatus.class));
        assertNotNull(jsonReader.read("allStichwort"));
    }

    @Test
    void applyRisiko() {
        final var value = Risiko.parseJson("{" +
                "\"titel\":\"Lorem ipsum\"," +
                "\"text\":\"Lorem ipsum dolor sit amet.\"," +
                "\"termin\":\"2021-04-22\"" +
                "}");
        final var dto = new VorgangDto(value);
        assertEquals(String.format("/risiko/%s", value.getId()), dto.getPageUri());
        assertTrue(dto.isAktiv());
        final var json = new JsonMapper().writeJson(dto);
        final var jsonReader = new JsonMapper().parseJsonPath(json);
        assertNotNull(jsonReader.read("id"));
        assertEquals(0, jsonReader.read("version", Integer.class));
        assertEquals(true, jsonReader.read("aktiv", Boolean.class));
        assertEquals(value.getTitel(), jsonReader.read("titel", String.class));
        assertEquals(value.getText(), jsonReader.read("text", String.class));
        assertEquals(value.getTermin(), jsonReader.read("termin", LocalDate.class));
        assertEquals(Terminserie.X, jsonReader.read("terminserie", Terminserie.class));
        assertEquals(value.getStatus(), jsonReader.read("status", VorgangStatus.class));
        assertNotNull(jsonReader.read("allStichwort"));
    }

    @Test
    void applySeite() {
        final var value = Seite.parseJson("{" +
                "\"uri\":\"README.adoc\"," +
                "\"titel\":\"README\"," +
                "\"text\":\"Lorem ipsum dolor sit amet.\"," +
                "\"aktiv\":\"true\"," +
                "\"termin\":\"2021-04-22\"" +
                "}");
        final var dto = new VorgangDto(value);
        assertEquals(String.format("/seite/%s", value.getId()), dto.getPageUri());
        assertTrue(dto.isAktiv());
        final var json = new JsonMapper().writeJson(dto);
        final var jsonReader = new JsonMapper().parseJsonPath(json);
        assertNotNull(jsonReader.read("id"));
        assertEquals(0, jsonReader.read("version", Integer.class));
        assertEquals(true, jsonReader.read("aktiv", Boolean.class));
        assertEquals(value.getTitel(), jsonReader.read("titel", String.class));
        assertEquals(value.getText(), jsonReader.read("text", String.class));
        assertEquals(value.getTermin(), jsonReader.read("termin", LocalDate.class));
        assertEquals(Terminserie.X, jsonReader.read("terminserie", Terminserie.class));
        assertEquals(VorgangStatus.I, jsonReader.read("status", VorgangStatus.class));
        assertNotNull(jsonReader.read("allStichwort"));
    }
}
