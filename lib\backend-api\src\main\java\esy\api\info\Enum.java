package esy.api.info;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import esy.json.JsonJpaUuidEntity;
import esy.json.JsonMapper;
import lombok.Getter;
import lombok.NonNull;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.PositiveOrZero;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Entity-Objekt für eine Aufzählung
 */
@Audited
@Entity
@Table(name = "enum", uniqueConstraints = {
        @UniqueConstraint(columnNames = {"id"}),
        @UniqueConstraint(columnNames = {"art", "code"}),
        @UniqueConstraint(columnNames = {"art", "name"})
})
public final class Enum extends JsonJpaUuidEntity<Enum> {

    /**
     * Art der Aufzählung (Diskriminator)
     */
    // tag::art[]
    @Column(name = "art")
    @Getter
    @JsonProperty
    @NotEmpty
    private String art;
    // end::art[]

    /**
     * Code eines Wertes.
     */
    // tag::code[]
    @Column(name = "code")
    @Getter
    @JsonProperty
    @PositiveOrZero
    private Long code;
    // end::code[]

    /**
     * Name eines Wertes (Kurzbezeichnung, Kürzel, Abkürzung)
     */
    // tag::name[]
    @Column(name = "name")
    @Getter
    @JsonProperty
    @NotEmpty
    private String name;
    // end::name[]

    /**
     * Text eines Wertes (Langbezeichnung, Beschreibung)
     */
    // tag::text[]
    @Column(name = "text")
    @Getter
    @JsonProperty
    @NotEmpty
    private String text;
    // end::text[]

    /**
     * Ist die Aufzählung konstant?
     */
    // tag::konstant[]
    @NotAudited
    @Column(name = "konstant")
    @Getter
    @JsonProperty
    private boolean konstant;
    // end::konstant[]

    Enum() {
        super();
        this.art = "";
        this.code = 0L;
        this.name = "";
        this.text = "";
        this.konstant = false;
    }

    Enum(@NonNull final Long version, @NonNull final UUID id) {
        super(version, id);
        this.art = "";
        this.code = 0L;
        this.name = "";
        this.text = "";
        this.konstant = false;
    }

    @Override
    public String toString() {
        return super.toString() + ",art='" + art + "'" + ",text='" + text + "'";
    }

    @Override
    public boolean isEqual(final Enum that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        return this.art.equals(that.art) &&
                this.code.equals(that.code) &&
                this.name.equals(that.name) &&
                this.text.equals(that.text) &&
                this.konstant == that.konstant;
    }

    @Override
    public Enum withId(@NonNull final UUID id) {
        if (getId().equals(id)) {
            return this;
        } else {
            return new Enum(getVersion(), id).merge(this);
        }
    }

    @Override
    public Enum merge(@NonNull final Enum that) {
        this.art = that.art;
        this.code = that.code;
        this.name = that.name;
        this.text = that.text;
        this.konstant = that.konstant;
        return this;
    }

    @JsonAnyGetter
    private Map<String, Object> extraJson() {
        final var allExtra = new HashMap<String, Object>();
        allExtra.put("version", getVersion());
        return allExtra;
    }

    @JsonProperty
    public String getValue() {
        return name;
    }

    @JsonIgnore
    public Enum setArt(@NonNull final String art) {
        this.art = art;
        return this;
    }

    @JsonIgnore
    public Enum setCode(@NonNull final Long code) {
        this.code = code;
        return this;
    }

    @JsonIgnore
    public Enum setName(@NonNull final String name) {
        this.name = name;
        return this;
    }

    @JsonIgnore
    public Enum setText(@NonNull final String text) {
        this.text = text;
        return this;
    }

    @Override
    public String writeJson() {
        return new JsonMapper().writeJson(this);
    }

    public static Enum parseJson(@NonNull final String json) {
        return new JsonMapper().parseJson(json, Enum.class);
    }

    public static Enum buildEnum() {
        final var item = new Enum();
        item.konstant = true;
        return item;
    }
}
