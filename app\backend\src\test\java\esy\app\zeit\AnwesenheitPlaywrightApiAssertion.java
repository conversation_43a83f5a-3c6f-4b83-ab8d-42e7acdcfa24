package esy.app.zeit;

import com.microsoft.playwright.Playwright;
import com.microsoft.playwright.options.RequestOptions;
import esy.api.team.Nutzer;
import esy.api.zeit.Anwesenheit;
import esy.api.zeit.Einsatztag;
import esy.app.PlaywrightApiAssertionBase;
import esy.app.PlaywrightApiTestEnv;
import esy.json.JsonMapper;
import lombok.NonNull;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;

import java.time.LocalTime;
import java.util.UUID;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.jupiter.api.Assertions.*;

@SuppressWarnings("java:S1192") // duplicating literals is ok
public class AnwesenheitPlaywrightApiAssertion extends PlaywrightApiAssertionBase {

    public AnwesenheitPlaywrightApiAssertion(@NonNull final Playwright playwright, @NonNull final PlaywrightApiTestEnv env) {
        super(playwright, env);
        login();
    }

    public void assertAnwesenheit() {
        final var nutzer1 = createRandomNutzer();

        final var einsatztag1 = Einsatztag.MO;
        final var anwesenheitId1 = doWithApi(
                (api) -> api.post("/api/anwesenheit", RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"einsatztag\":\"%s\",\"nutzerId\":\"%s\",\"planStart\":\"07:30\",\"planDauer\":\"240\"}",
                                einsatztag1, nutzer1.getId()))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.CREATED.value()));
                    final var json = Anwesenheit.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(nutzer1.getId(), json.getNutzerId());
                    assertEquals(einsatztag1, json.getEinsatztag());
                    assertEquals(LocalTime.of(7, 30), json.getPlanStart());
                    assertEquals(240L, json.getPlanDauer());
                    return json.getId();
                });

        final var einsatztag2 = Einsatztag.DI;
        final var anwesenheitId2 = doWithApi(
                (api) -> api.put("/api/anwesenheit/" + UUID.randomUUID(), RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"einsatztag\":\"%s\",\"nutzerId\":\"%s\"}",
                                einsatztag2, nutzer1.getId()))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.CREATED.value()));
                    final var json = Anwesenheit.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(nutzer1.getId(), json.getNutzerId());
                    assertEquals(einsatztag2, json.getEinsatztag());
                    assertEquals(LocalTime.of(9, 0), json.getPlanStart());
                    assertEquals(540L, json.getPlanDauer());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.get("/api/anwesenheit"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var jsonReader = new JsonMapper().parseJsonPath(res.text());
                    assertNotNull(jsonReader.read("$.page"));
                    final var allAnwesenheitId = jsonReader.readContentId();
                    assertTrue(allAnwesenheitId.contains(anwesenheitId1.toString()));
                    assertTrue(allAnwesenheitId.contains(anwesenheitId2.toString()));
                    return allAnwesenheitId;
                });

        doWithApi(
                (api) -> api.delete("/api/anwesenheit/" + anwesenheitId1),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Anwesenheit.parseJson(res.text());
                    assertEquals(anwesenheitId1, json.getId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.delete("/api/anwesenheit/" + anwesenheitId2),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Anwesenheit.parseJson(res.text());
                    assertEquals(anwesenheitId2, json.getId());
                    return json.getId();
                });
    }

    public void assertAnwesenheitBesitzer() {
        final var nutzer1 = createRandomNutzer();
        final var nutzer2 = createRandomNutzer();

        final var einsatztag1 = Einsatztag.MO;
        final var anwesenheitId = doWithApi(
                (api) -> api.post("/api/anwesenheit", RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"einsatztag\":\"%s\",\"nutzerId\":\"%s\",\"planStart\":\"07:30\",\"planDauer\":\"240\"}",
                                einsatztag1, nutzer1.getId()))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.CREATED.value()));
                    final var json = Anwesenheit.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(nutzer1.getId(), json.getNutzerId());
                    assertEquals(einsatztag1, json.getEinsatztag());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.get("/api/anwesenheit/" + anwesenheitId + "/nutzer"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Nutzer.parseJson(res.text());
                    assertEquals(nutzer1.getId(), json.getId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.patch("/api/anwesenheit/" + anwesenheitId, RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, "application/merge-patch+json")
                        .setData(String.format("{\"nutzerId\":\"%s\"}",
                                nutzer2.getId()))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Anwesenheit.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(nutzer2.getId(), json.getNutzerId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.get("/api/anwesenheit/" + anwesenheitId + "/nutzer"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Nutzer.parseJson(res.text());
                    assertEquals(nutzer2.getId(), json.getId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.delete("/api/anwesenheit/" + anwesenheitId),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Anwesenheit.parseJson(res.text());
                    assertEquals(anwesenheitId, json.getId());
                    return json.getId();
                });
    }
}
