:icons: font
:experimental: true
= HELPME

== PostgreSQL

https://www.postgresql.org

https://www.pgadmin.org

https://www.adminer.org/

https://cloud.google.com/sql/docs/postgres

https://aws.amazon.com/de/rds/postgresql

== Liquibase

Der Befehl

[source, bash, subs=attributes+, options="nowrap"]
----
$ liquibase validate \
  --changelog-file=lib/backend-data/src/main/resources/liquibase/changelog.xml \
  --url=***************************************** \
  --username=sa \
  --password=P@ssw0rd  
----

https://docs.liquibase.com/commands/update/validate.html

validiert die Skripte für die Datenbank.

Der Befehl

[source, bash, subs=attributes+, options="nowrap"]
----
$ liquibase db-doc build/liquibase-html \
  --changelog-file=lib/backend-data/src/main/resources/liquibase/changelog.xml \
  --url=***************************************** \
  --username=sa \
  --password=P@ssw0rd  
----

erzeugt eine Dokumentation der Datenbank.

https://docs.liquibase.com/commands/change-tracking/db-doc.html

Der Befehl

[source, bash, subs=attributes+, options="nowrap"]
----
$ liquibase generate-changelog \
  --changelog-file=build/liquibase-changelog.xml \
  --url=***************************************** \
  --username=sa \
  --password=P@ssw0rd
----

erzeugt Skripte für die Datenbank.

https://docs.liquibase.com/commands/inspection/generate-changelog.html
