package esy.app.plan;

import com.microsoft.playwright.Playwright;
import com.microsoft.playwright.options.RequestOptions;
import esy.api.plan.Risiko;
import esy.api.team.Nutzer;
import esy.app.PlaywrightApiAssertionBase;
import esy.app.PlaywrightApiTestEnv;
import lombok.NonNull;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;

import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.jupiter.api.Assertions.*;

@SuppressWarnings("java:S1192") // duplicating literals is ok
public class RisikoPlaywrightApiAssertion extends PlaywrightApiAssertionBase {

    public RisikoPlaywrightApiAssertion(@NonNull final Playwright playwright, @NonNull final PlaywrightApiTestEnv env) {
        super(playwright, env);
        login();
    }

    public void assertRisiko() {
        final var risikoTitel1 = "Risiko A";
        final var risikoText1 = "Lorem Ipsum.";
        final var risikoId1 = doWithApi(
                (api) -> api.post("/api/risiko", RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"titel\":\"%s\",\"text\":\"%s\"}",
                                risikoTitel1, risikoText1))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.CREATED.value()));
                    final var json = Risiko.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(risikoTitel1, json.getTitel());
                    assertEquals(risikoText1, json.getText());
                    assertNotNull(json.getStatus());
                    return json.getId();
                });

        final var risikoTitel2 = "Risiko B";
        final var risikoText2 = "Lorem Ipsum dolor med.";
        final var risikoId2 = doWithApi(
                (api) -> api.post("/api/risiko", RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .setData(String.format("{\"titel\":\"%s\",\"text\":\"%s\"}",
                                risikoTitel2, risikoText2))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.CREATED.value()));
                    final var json = Risiko.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(risikoTitel2, json.getTitel());
                    assertEquals(risikoText2, json.getText());
                    assertNotNull(json.getStatus());
                    return json.getId();
                });

        List.of("X", "I", "B", "B", "A", "A").forEach(status -> {
            doWithApi(
                    (api) -> api.patch("/api/risiko/" + risikoId2, RequestOptions.create()
                            .setHeader(HttpHeaders.CONTENT_TYPE, "application/merge-patch+json")
                            .setData("{\"status\":\"" + status + "\"}")),
                    (res) -> {
                        assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                        final var json = Risiko.parseJson(res.text());
                        assertNotNull(json.getId());
                        assertEquals(status, json.getStatus().name());
                        return json.getId();
                    });
        });

        doWithApi(
                (api) -> api.delete("/api/risiko/" + risikoId1),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Risiko.parseJson(res.text());
                    assertEquals(risikoId1, json.getId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.delete("/api/risiko/" + risikoId2),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Risiko.parseJson(res.text());
                    assertEquals(risikoId2, json.getId());
                    return json.getId();
                });
    }

    public void assertRisikoNutzer() {
        final var nutzer1 = createRandomNutzer();
        final var nutzer2 = createRandomNutzer();
        final var risiko = createRandomRisiko();
        assertNull(risiko.getNutzerId());

        doWithApi(
                (api) -> api.get("/api/risiko/" + risiko.getId() + "/nutzer"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.NOT_FOUND.value()));
                    return null;
                });

        doWithApi(
                (api) -> api.patch("/api/risiko/" + risiko.getId(), RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, "application/merge-patch+json")
                        .setData(String.format("{\"nutzerId\":\"%s\"}",
                                nutzer1.getId()))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Risiko.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(nutzer1.getId(), json.getNutzerId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.get("/api/risiko/" + risiko.getId() + "/nutzer"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Nutzer.parseJson(res.text());
                    assertEquals(nutzer1.getId(), json.getId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.patch("/api/risiko/" + risiko.getId(), RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, "application/merge-patch+json")
                        .setData(String.format("{\"nutzerId\":\"%s\"}",
                                nutzer2.getId()))),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Risiko.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertEquals(nutzer2.getId(), json.getNutzerId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.get("/api/risiko/" + risiko.getId() + "/nutzer"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Nutzer.parseJson(res.text());
                    assertEquals(nutzer2.getId(), json.getId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.patch("/api/risiko/" + risiko.getId(), RequestOptions.create()
                        .setHeader(HttpHeaders.CONTENT_TYPE, "application/merge-patch+json")
                        .setData("{\"nutzerId\":null}")),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                    final var json = Risiko.parseJson(res.text());
                    assertNotNull(json.getId());
                    assertNull(json.getNutzerId());
                    return json.getId();
                });

        doWithApi(
                (api) -> api.get("/api/risiko/" + risiko.getId() + "/nutzer"),
                (res) -> {
                    assertThat(res.status(), equalTo(HttpStatus.NOT_FOUND.value()));
                    return null;
                });
    }
}
