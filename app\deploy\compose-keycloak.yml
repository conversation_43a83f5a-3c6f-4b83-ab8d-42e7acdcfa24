services:
  keycloak:
    depends_on:
      - keycloak-pg
    hostname: keycloak
    image: quay.io/keycloak/keycloak:26.1.3@sha256:2ce6c7c70994c70dbbd70b372a5422c3b4eebb32583175eac03751320609e52c
    ports:
      - 9080:8080
      - 9443:8443
    command:
      - start-dev 
      - --import-realm
      - --verbose
    volumes:
      - ./compose-keycloak-realm.json:/opt/keycloak/data/import/realm.json
    environment:
      KC_BOOTSTRAP_ADMIN_USERNAME: sa
      KC_BOOTSTRAP_ADMIN_PASSWORD: P@ssw0rd
      KC_HOSTNAME_DEBUG: true
      KC_HOSTNAME_STRICT: false
      KC_HTTP_ENABLED: true
      KC_DB: postgres
      KC_DB_URL: *******************************************
      KC_DB_USERNAME: sa
      KC_DB_PASSWORD: P@ssw0rd
  keycloak-pg:
    hostname: keycloak-pg
    image: postgres:17@sha256:6efd0df010dc3cb40d5e33e3ef84acecc5e73161bd3df06029ee8698e5e12c60
    ports:
      - 9432:5432
    volumes:
      - keycloak-pg:/var/lib/postgresql/data
    environment:
      - POSTGRES_DB=postgres
      - POSTGRES_USER=sa
      - POSTGRES_PASSWORD=P@ssw0rd
volumes:
  keycloak-pg:
